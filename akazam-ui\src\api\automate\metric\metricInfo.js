import request from '@/utils/request'

// 查询指标信息列表
export function listMetricInfo(query) {
  return request({
    url: '/metric/metricInfo/list',
    method: 'get',
    params: query || { pageSize: 9999 }
  })
}

// 查询指标信息详细
export function getMetricInfo(id) {
  return request({
    url: '/metric/metricInfo/' + id,
    method: 'get'
  })
}

// 新增指标信息
export function addMetricInfo(data) {
  return request({
    url: '/metric/metricInfo',
    method: 'post',
    data: data
  })
}

// 修改指标信息
export function updateMetricInfo(data) {
  return request({
    url: '/metric/metricInfo',
    method: 'put',
    data: data
  })
}

// 删除指标信息
export function delMetricInfo(ids) {
  return request({
    url: '/metric/metricInfo/batch/' + ids,
    method: 'delete'
  })
}
