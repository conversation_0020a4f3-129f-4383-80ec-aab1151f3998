import request from '@/utils/request'

// 查询攻击日志告警策略列表
export function listRule(query) {
    return request({
        url: '/alarm/log/rule/list',
        method: 'get',
        params: query
    })
}

// 查询攻击日志告警策略详细
export function getRule(id) {
    return request({
        url: '/alarm/log/rule/' + id,
        method: 'get'
    })
}

// 新增攻击日志告警策略
export function addRule(data) {
    return request({
        url: '/alarm/log/rule',
        method: 'post',
        data: data
    })
}

// 修改攻击日志告警策略
export function updateRule(data) {
    return request({
        url: '/alarm/log/rule',
        method: 'put',
        data: data
    })
}

// 删除攻击日志告警策略
export function delRule(id) {
    return request({
        url: '/alarm/log/rule/' + id,
        method: 'delete'
    })
}
