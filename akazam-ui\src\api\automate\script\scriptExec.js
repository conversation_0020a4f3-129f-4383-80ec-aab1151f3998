import request from '@/utils/request'
import { saveAs } from 'file-saver'
import { blobValidate } from '@/utils/akazam'
import { ElLoading, ElMessage } from 'element-plus'

// 查询脚本执行列表
export function listScriptExec(query) {
  return request({
    url: '/script/scriptExec/list',
    method: 'get',
    params: query
  })
}

// 查询脚本执行详细
export function getScriptExec(id) {
  return request({
    url: '/script/scriptExec/' + id,
    method: 'get'
  })
}

// 新增脚本执行
export function addScriptExec(data) {
  return request({
    url: '/script/scriptExec',
    method: 'post',
    data: data
  })
}

// 修改脚本执行
export function updateScriptExec(data) {
  return request({
    url: '/script/scriptExec',
    method: 'put',
    data: data
  })
}

// 删除脚本执行
export function delScriptExec(id) {
  return request({
    url: '/script/scriptExec/' + id,
    method: 'delete'
  })
}

// 脚本执行
export function execScript(data) {
  return request({
    url: '/script/scriptExec/exec',
    method: 'post',
    data: data
  })
}

// 脚本执行历史
export function getExecScriptRes(data) {
  return request({
    url: '/script/scriptExec/idList',
    method: 'post',
    data: data
  })
}


// 远程文件上传
export function uploadSfp(data) {
  return request({
    url: '/script/scriptExec/uploadSfp',
    method: 'post',
    data: data
  })
}

// 脚本文件上传
export function uploadFile(data) {
  return request({
    url: '/script/scriptExec/uploadFile',
    method: 'post',
    data: data
  })
}

// 日志下载
export function downLog(query, filename) {
  const downloadLoadingInstance = ElLoading.service({ text: "正在下载，请稍候", background: "rgba(0, 0, 0, 0.7)", })

  return request({
    url: '/script/scriptExec/downLog',
    method: 'post',
    data: query,
    responseType: 'blob',
  }).then(async (data) => {
    const isBlob = blobValidate(data);
    if (isBlob) {
      const blob = new Blob([data])
      saveAs(blob, filename)
    } else {
      const resText = await data.text();
      const rspObj = JSON.parse(resText);
      const errMsg = errorCode[rspObj.code] || rspObj.msg || errorCode['default']
      ElMessage.error(errMsg);
    }
    downloadLoadingInstance.close();
  }).catch((r) => {
    console.error(r)
    ElMessage.error('下载文件出现错误，请联系管理员！')
    downloadLoadingInstance.close();
  })
}


// 查询远程文件上传表列表
export function listScriptUpload(query) {
  return request({
    url: '/script/sfpUpload/list',
    method: 'get',
    params: query
  })
}