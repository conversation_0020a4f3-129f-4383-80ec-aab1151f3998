import request from '@/utils/request'

// 查询作业执行告警管理列表
export function listAlarmmanage(query) {
    return request({
        url: '/job/alarmmanage/list',
        method: 'get',
        params: query
    })
}

// 查询作业执行告警管理详细
export function getAlarmmanage(id) {
    return request({
        url: '/job/alarmmanage/' + id,
        method: 'get'
    })
}

// 新增作业执行告警管理
export function addAlarmmanage(data) {
    return request({
        url: '/job/alarmmanage',
        method: 'post',
        data: data
    })
}

// 修改作业执行告警管理
export function updateAlarmmanage(data) {
    return request({
        url: '/job/alarmmanage',
        method: 'put',
        data: data
    })
}

// 删除作业执行告警管理
export function delAlarmmanage(id) {
    return request({
        url: '/job/alarmmanage/' + id,
        method: 'delete'
    })
}
