import request from "@/utils/request";

// 查询事件单列表
export function test(query) {
    return request({
        url: "/fault/detect/vm-lag",
        method: "post",
        data: query,
        timeout: 60000,
    });
}

// vm登录探测
export function vmDetect(query, devjson) {
    return request({
        url: "/fault/detect/vm-detect",
        method: "post",
        data: query,
        timeout: 60000,
        devjson: devjson ? devjson : null
    });
}

export function linkPort(query) {
    return request({
        url: "/fault/detect/link-port",
        method: "post",
        data: query,
        timeout: 60000,
    });
}

export function bgpIsis(query) {
    return request({
        url: "/fault/detect/bgp-isis",
        method: "post",
        data: query,
        timeout: 60000,
    });
}

export function linePort(query) {
    return request({
        url: "/fault/detect/line-port",
        method: "post",
        data: query,
        timeout: 60000,
    });
}

export function special(query, devjson) {
    return request({
        url: "/fault/detect/special",
        method: "post",
        data: query,
        timeout: 60000,
        devjson: devjson ? devjson : false
    });
}

export function waf(query) {
    return request({
        url: "/fault/detect/waf",
        method: "post",
        data: query,
        timeout: 60000,
    });
}

export function vmIp(query, devjson) {
    return request({
        url: "/fault/detection/vmIp",
        method: "post",
        data: query,
        timeout: 60000,
        devjson: devjson ? devjson : false
    });
}


export function slowIO(query, devjson) {
    return request({
        url: "/fault/detection/slowIO",
        method: "post",
        data: query,
        timeout: 60000,
        devjson: devjson ? devjson : null
    });
}

export function noInfo(query, devjson) {
    return request({
        url: "/fault/detection/noInfo",
        method: "post",
        data: query,
        timeout: 60000,
        devjson: devjson ? devjson : null
    });
}


// export function access(query) {
//   return request({
//     url: "/fault/detection/access",
//     method: "post",
//     data: query,
//     timeout: 60000,
//   });
// }

export function access(query) {
    return request({
        url: "/fault/detection/access",
        method: "get",
        params: query,
        timeout: 60000,
    });
}


// 查询指令集选择框
export function faultCommandSelect(query) {
    return request({
        url: '/fault/command/select',
        method: 'get',
        params: query
    })
}

// 探测
export function faultDetectDetect(query, devjson) {
    return request({
        url: "/fault/detect/detect",
        method: "post",
        data: query,
        timeout: 30000,
        devjson: devjson ? devjson : false
    });
}

export function faultDetectRun(query) {
    return request({
        url: "/fault/detect/run",
        method: "post",
        data: query,
        timeout: 600000,
    });
}

export function getProjectByIp(ip) {
    if (ip && ip.trim() !== '') {
        if (ip.includes('/')) {
            ip = ip.split('/')[0]; // 保留 / 前面的部分
        }
        return request({
            url: '/fault/detect/getProject/' + ip,
            method: 'get'
        })
    }
}

export function metricDataBatchQuery(query, devjson) {
    return request({
        url: "/metric/data/batch/query",
        method: "post",
        data: query,
        timeout: 600000,
        devjson: devjson ? devjson : null
    });
}

export function submitFaultOrder(query, devjson) {
    return request({
        url: "/fault/detect/event-list",
        method: "post",
        data: query,
        timeout: 600000,
        devjson: devjson ? devjson : null
    });
}

export function searchProjects(query) {
    return request({
        url: '/fault/detect/search-projects',
        method: 'get',
        params: query,
        timeout: 30000
    });
}
