import request from '@/utils/request'

// 查询作业分组关系列表
export function listJob_group_relation(query) {
  return request({
    url: '/automate/job_group_relation/list',
    method: 'get',
    params: query
  })
}

// 查询作业分组关系详细
export function getJob_group_relation(id) {
  return request({
    url: '/automate/job_group_relation/' + id,
    method: 'get'
  })
}

// 新增作业分组关系
export function addJob_group_relation(data) {
  return request({
    url: '/automate/job_group_relation',
    method: 'post',
    data: data
  })
}

// 修改作业分组关系
export function updateJob_group_relation(data) {
  return request({
    url: '/automate/job_group_relation',
    method: 'put',
    data: data
  })
}

// 删除作业分组关系
export function delJob_group_relation(id) {
  return request({
    url: '/automate/job_group_relation/' + id,
    method: 'delete'
  })
}
