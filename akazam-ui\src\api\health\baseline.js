import request from '@/utils/request'

// 查询动态基线列表
export function listBaseline(query) {
  return request({
    url: '/health/baseline/list',
    method: 'get',
    params: query
  })
}

// 查询动态基线详细
export function getBaseline(id) {
  return request({
    url: '/health/baseline/' + id,
    method: 'get'
  })
}

// 新增动态基线
export function addBaseline(data) {
  return request({
    url: '/health/baseline',
    method: 'post',
    data: data
  })
}

// 修改动态基线
export function updateBaseline(data) {
  return request({
    url: '/health/baseline',
    method: 'put',
    data: data
  })
}

// 删除动态基线
export function delBaseline(id) {
  return request({
    url: '/health/baseline/' + id,
    method: 'delete'
  })
}


export function infoBaseline(query) {
  return request({
    url: '/health/baseline/info',
    method: 'get',
    params: query,
    timeout: 600000,
  })
}

export function infoBaselineHistory(query, devjson) {
  return request({
    url: '/health/baseline/history',
    method: 'get',
    params: query,
    timeout: 600000,
    devjson: devjson ? devjson : false
  })
}