import request from '@/utils/request'

// 查询告警列表
export function listInfo(query) {
  return request({
    url: '/alarm/info/list',
    method: 'get',
    params: query
  })
}

// 查询告警详细
export function getInfo(id) {
  return request({
    url: '/alarm/info/' + id,
    method: 'get'
  })
}

// 新增告警
export function addInfo(data) {
  return request({
    url: '/alarm/info',
    method: 'post',
    data: data
  })
}

// 修改告警
export function updateInfo(data) {
  return request({
    url: '/alarm/info',
    method: 'put',
    data: data
  })
}

// 删除告警
export function delInfo(id) {
  return request({
    url: '/alarm/info/' + id,
    method: 'delete'
  })
}
