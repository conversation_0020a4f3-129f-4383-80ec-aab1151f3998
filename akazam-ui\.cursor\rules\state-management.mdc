---
description: 
globs: 
alwaysApply: false
---
# 状态管理指南

Akazam 平台同时使用 Vuex 和 Pinia 进行状态管理。

## Vuex 状态管理

传统的 Vuex 状态管理位于 `src/store` 目录下，主要用于管理全局状态：

- `src/store/index.js`: Vuex 入口文件
- `src/store/getters.js`: 全局 getters
- `src/store/modules/`: 各模块状态管理

### 主要模块

- `app`: 应用全局配置
- `user`: 用户信息和权限
- `tagsView`: 标签页管理
- `permission`: 权限和路由管理
- `settings`: 系统设置

## Pinia 状态管理

新的 Pinia 状态管理位于 `src/stores` 目录下，推荐在新功能中使用 Pinia：

- `src/stores/index.js`: Pinia 入口文件
- `src/stores/modules/`: 各模块状态管理

### 使用 Vuex 示例

```javascript
import { useStore } from 'vuex'
import { computed } from 'vue'

export default {
  setup() {
    const store = useStore()
    
    // 获取状态
    const userName = computed(() => store.state.user.name)
    
    // 提交 mutation
    const updateUserName = (name) => {
      store.commit('SET_NAME', name)
    }
    
    // 分发 action
    const login = (userInfo) => {
      store.dispatch('user/login', userInfo)
    }
    
    return {
      userName,
      updateUserName,
      login
    }
  }
}
```

### 使用 Pinia 示例

```javascript
import { useUserStore } from '@/stores/modules/user'
import { storeToRefs } from 'pinia'

export default {
  setup() {
    const userStore = useUserStore()
    
    // 使用 storeToRefs 获取响应式状态
    const { name, roles } = storeToRefs(userStore)
    
    // 调用 action
    const login = () => {
      userStore.login(userInfo)
    }
    
    // 直接修改状态
    const updateName = (newName) => {
      userStore.name = newName
    }
    
    return {
      name,
      roles,
      login,
      updateName
    }
  }
}
```

## 状态管理规范

1. 新功能优先使用 Pinia
2. 状态按模块划分，避免全局状态过大
3. 使用计算属性获取状态
4. 异步操作放在 actions 中处理
5. 避免在组件中直接修改状态

