import request from '@/utils/request'

export function getProjects(query) {
  return request({
    url: '/fault/fullLink/detect/get-projects',
    method: 'get',
    params: query
  })
}

export function getSelectProjects(query) {
  return request({
    url: '/fault/fullLink/detect/projects/select',
    method: 'get',
    params: query
  })
}

export function getProjectsNode(query) {
  return request({
    url: '/fault/fullLink/detect/get-projects-node',
    method: 'get',
    params: query
  })
}

export function projectNodeNet(query, devjson) {
  return request({
    url: `/fault/fullLink/detect/${query.value}`,
    method: 'get',
    timeout: 300000,
    devjson: devjson ? devjson : false
  })
}

export function getTopology(query, devjson) {
  return request({
    url: `/fault/fullLink/detect/topology/${query}`,
    method: 'get',
    timeout: 300000,
    devjson: devjson ? devjson : false
  })
}


export function getEipZone(query) {
  return request({
    url: '/eip/zone',
    method: 'get',
    params: query
  })
}

export function eipZoneEip(query) {
  return request({
    url: `/eip/dashboard?zone=${query.zone}&eip=${query.eip}&capturePointId=${query.capturePointId}&startTime=${query.startTime}&endTime=${query.endTime}`,
    method: 'get',
    timeout: 900000,
  })
}

// 批量虚拟机检测接口，替代原有的slowIO接口，一次性查询多个虚拟机信息
export function batchVmDetect(ipArray, devjson) {
  return request({
    url: '/fault/detection/batchVmDetect',
    method: 'post',
    data: ipArray, // 直接传递IP数组作为请求体，不使用其他参数
    timeout: 600000,
    devjson: devjson ? devjson : false
  })
}

export function getDeviceList() {
  return request({
    url: '/eip/captureDevice',
    method: 'get',
  })
}

export function getCapturePoint(zone) {
  return request({
    url: '/eip/capturePoint/' + zone,
    method: 'get',
  })
}
