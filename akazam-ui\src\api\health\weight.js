import request from '@/utils/request'

// 查询指标权重配置列表
export function listWeight(query) {
  return request({
    url: '/health/weight/list',
    method: 'get',
    params: query
  })
}

export function listWeightGroupByName(query) {
  return request({
    url: '/health/weight/listGroupByName',
    method: 'get',
    params: query
  })
}

// 查询指标权重配置详细
export function getWeight(id) {
  return request({
    url: '/health/weight/' + id,
    method: 'get'
  })
}

// 新增指标权重配置
export function addWeight(data) {
  return request({
    url: '/health/weight',
    method: 'post',
    data: data
  })
}

// 修改指标权重配置
export function updateWeight(data) {
  return request({
    url: '/health/weight',
    method: 'put',
    data: data
  })
}

// 删除指标权重配置
export function delWeight(id) {
  return request({
    url: '/health/weight/' + id,
    method: 'delete'
  })
}
