import request from '@/utils/request'

// 查询云管告警处置配置列表
export function listHandleRule(query) {
  return request({
    url: '/alarm/handleRule/list',
    method: 'get',
    params: query
  })
}

// 查询云管告警处置配置详细
export function getHandleRule(id) {
  return request({
    url: '/alarm/handleRule/' + id,
    method: 'get'
  })
}

// 新增云管告警处置配置
export function addHandleRule(data) {
  return request({
    url: '/alarm/handleRule',
    method: 'post',
    data: data
  })
}

// 修改云管告警处置配置
export function updateHandleRule(data) {
  return request({
    url: '/alarm/handleRule',
    method: 'put',
    data: data
  })
}

// 删除云管告警处置配置
export function delHandleRule(id) {
  return request({
    url: '/alarm/handleRule/' + id,
    method: 'delete'
  })
}
