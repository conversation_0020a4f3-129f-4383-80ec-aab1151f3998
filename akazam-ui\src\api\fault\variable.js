import request from '@/utils/request'

// 查询故障探测-指令集变量列表
export function listVariable(query) {
  return request({
    url: '/fault/variable/list',
    method: 'get',
    params: query
  })
}

// 查询故障探测-指令集变量详细
export function getVariable(id) {
  return request({
    url: '/fault/variable/' + id,
    method: 'get'
  })
}

// 新增故障探测-指令集变量
export function addVariable(data) {
  return request({
    url: '/fault/variable',
    method: 'post',
    data: data
  })
}

// 修改故障探测-指令集变量
export function updateVariable(data) {
  return request({
    url: '/fault/variable',
    method: 'put',
    data: data
  })
}

// 删除故障探测-指令集变量
export function delVariable(id) {
  return request({
    url: '/fault/variable/' + id,
    method: 'delete'
  })
}
