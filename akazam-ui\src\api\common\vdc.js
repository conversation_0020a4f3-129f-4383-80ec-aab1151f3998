import request from '@/utils/request'

// 查询VDC列表
export function listVdc(query) {
  return request({
    url: '/common/vdc/list',
    method: 'get',
    params: query
  })
}

// 查询VDC详细
export function getVdc(id) {
  return request({
    url: '/common/vdc/' + id,
    method: 'get'
  })
}

// 新增VDC
export function addVdc(data) {
  return request({
    url: '/common/vdc',
    method: 'post',
    data: data
  })
}

// 修改VDC
export function updateVdc(data) {
  return request({
    url: '/common/vdc',
    method: 'put',
    data: data
  })
}

// 删除VDC
export function delVdc(id) {
  return request({
    url: '/common/vdc/' + id,
    method: 'delete'
  })
}
