---
description: 
globs: 
alwaysApply: false
---
# 组件指南

Akazam 平台使用了大量自定义组件，这些组件分为以下几类：

## 核心组件

- **DifyChat**: 基于 Dify 的聊天界面组件
- **Terminal**: 终端模拟器组件，基于 xterm
- **SvgIcon**: SVG 图标组件，用于显示项目中的图标
- **AceEditor**: 代码编辑器组件，基于 Ace
- **Topo**: 拓扑图组件

## 表单组件

- **TreeSelect**: 树形选择组件
- **DeviceSelect**: 设备选择组件
- **UserSelect**: 用户选择组件
- **SizeSelect**: 尺寸选择组件
- **Pagination**: 分页组件

## 可视化组件

- **EcView**: ECharts 图表封装组件
- **InteractiveLines**: 交互式连线组件
- **VueMatrixDigitRain**: 数字雨效果组件

## 业务组件

- **AutoText**: 自动文本生成组件
- **Crontab**: 定时任务组件
- **FileUpload**: 文件上传组件
- **ImageUpload**: 图片上传组件
- **VoiceAlarm**: 语音告警组件

## 布局组件

- **Breadcrumb**: 面包屑导航组件
- **HeaderSearch**: 头部搜索组件
- **TopNav**: 顶部导航组件
- **RightToolbar**: 右侧工具栏组件
- **Hamburger**: 汉堡菜单组件
- **Screenfull**: 全屏切换组件

## 组件风格指南

开发新组件时，应遵循以下规则：

1. 组件名使用 PascalCase 命名
2. 组件文件使用单文件组件 (`.vue`) 格式
3. 复杂组件应使用文件夹组织，包含 `index.vue` 作为入口
4. 组件应具有清晰的 props 和 emits 定义
5. 尽可能使用 Composition API

