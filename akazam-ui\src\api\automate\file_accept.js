import request from '@/utils/request'

// 查询配置验收列表
export function listFileAccept(query) {
  return request({
    url: '/automate/file/accept/list',
    method: 'get',
    params: query
  })
}

// 查询配置验收详细
export function getFileAccept(id) {
  return request({
    url: '/automate/file/accept/' + id,
    method: 'get'
  })
}

// 新增配置验收
export function addFileAccept(data) {
  return request({
    url: '/automate/file/accept',
    method: 'post',
    data: data
  })
}

// 修改配置验收
export function updateFileAccept(data) {
  return request({
    url: '/automate/file/accept',
    method: 'put',
    data: data
  })
}

// 删除配置验收
export function delFileAccept(id) {
  return request({
    url: '/automate/file/accept/' + id,
    method: 'delete'
  })
}
