import request from '@/utils/request'

// 查询任务管理列表
export function listTask(query) {
  return request({
    url: '/automate/task/list',
    method: 'get',
    params: query
  })
}

// 查询任务管理详细
export function getTask(id) {
  return request({
    url: '/automate/task/' + id,
    method: 'get'
  })
}

// 新增任务管理
export function addTask(data) {
  return request({
    url: '/automate/task',
    method: 'post',
    data: data
  })
}

// 修改任务管理
export function updateTask(data) {
  return request({
    url: '/automate/task',
    method: 'put',
    data: data
  })
}

// 删除任务管理
export function delTask(id) {
  return request({
    url: '/automate/task/' + id,
    method: 'delete'
  })
}

// 执行任务
export function executeTask(id) {
  return request({
    url: '/automate/task/run/' + id,
    method: 'post'
  })
}

// 详情页面选择文件验收后返回页面需要的信息 /automate/task/file/accept/info/1,3
export function getTaskFileAcceptInfo(ids) {
  return request({
    url: '/automate/task/file/accept/info/' + ids,
    method: 'get'
  })
}

// 详情页面选择文件验收后返回页面需要的信息 automate/task/config/accept/info/1,3
export function getTaskConfigAcceptInfo(ids) {
  return request({
    url: '/automate/task/config/accept/info/' + ids,
    method: 'get'
  })
}
