---
description: 
globs: 
alwaysApply: false
---
# 路由与权限指南

Akazam 平台使用 Vue Router 进行路由管理，并实现了基于角色的权限控制系统。

## 路由配置

路由配置文件位于 [src/router/index.js](mdc:src/router/index.js)，分为以下几类：

- **constantRoutes**: 公共路由，所有用户都可以访问
- **dynamicRoutes**: 动态路由，基于用户权限动态加载

## 路由元数据

路由配置中的元数据 (meta) 用于控制路由的行为：

```javascript
{
  path: '/example',
  component: Layout,
  children: [{
    path: 'index',
    component: () => import('@/views/example/index'),
    name: 'Example',
    meta: {
      title: '示例页面',      // 页面标题，显示在侧边栏和面包屑
      icon: 'example',       // 图标名称
      noCache: false,        // 是否不缓存页面
      affix: false,          // 是否固定在标签页
      breadcrumb: true,      // 是否显示在面包屑
      activeMenu: '/example' // 激活的菜单项
    }
  }]
}
```

## 权限控制

权限控制机制实现在 [src/permission.js](mdc:src/permission.js) 中，主要包括：

1. 路由导航守卫
2. 动态路由生成
3. 权限验证
4. 路由加载状态管理

## 权限类型

系统支持两种权限控制方式：

1. **角色权限**: 根据用户角色确定可访问的路由
   ```javascript
   roles: ['admin', 'editor']
   ```

2. **菜单权限**: 更细粒度的功能权限控制
   ```javascript
   permissions: ['system:user:list', 'system:user:create']
   ```

## 权限使用示例

```vue
<template>
  <div>
    <!-- 根据角色显示 -->
    <el-button v-if="hasRole(['admin'])">管理员按钮</el-button>
    
    <!-- 根据权限显示 -->
    <el-button v-if="hasPermi('system:user:add')">新增用户</el-button>
  </div>
</template>

<script setup>
import { useUserStore } from '@/store/modules/user';
import { hasRole, hasPermi } from '@/utils/permission';
</script>
```

## 权限指令

系统提供了权限指令，可以直接在模板中使用：

- `v-hasRole`: 角色权限指令
- `v-hasPermi`: 菜单权限指令

```vue
<el-button v-hasRole="['admin']">管理员按钮</el-button>
<el-button v-hasPermi="['system:user:add']">新增用户</el-button>
```

