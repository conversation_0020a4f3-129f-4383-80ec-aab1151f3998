import request from '@/utils/request'

// 查询告警中心-重要业务告警-告警管理列表
export function listAlarm(query, devjson) {
  return request({
    url: '/alarm/business/dial/list',
    method: 'get',
    params: query,
    devjson: devjson ? devjson : null
  })
}

// 查询告警中心-重要业务告警-告警管理详细
export function getAlarm(id) {
  return request({
    url: '/alarm/business/dial/' + id,
    method: 'get'
  })
}

// 新增告警中心-重要业务告警-告警管理
export function addAlarm(data) {
  return request({
    url: '/alarm/business/dial',
    method: 'post',
    data: data
  })
}

// 修改告警中心-重要业务告警-告警管理
export function updateAlarm(data) {
  return request({
    url: '/alarm/business/dial',
    method: 'put',
    data: data
  })
}

// 删除告警中心-重要业务告警-告警管理
export function delAlarm(id) {
  return request({
    url: '/alarm/business/dial/' + id,
    method: 'delete'
  })
}

// 响应超阈值告警
export function responseAlarm(ids) {
  return request({
    url: `/alarm/business/threshold/response/${ids}`,
    method: 'put'
  })
}
// 响应超阈值告警
export function responseAlarmBc(ids) {
  return request({
    url: `/alarm/business/dial/response/${ids}`,
    method: 'put'
  })
}
