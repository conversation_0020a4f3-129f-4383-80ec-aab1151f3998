import request from '@/utils/request'

// 查询网络拨测列表
export function listNet(query) {
  return request({
    url: '/probe/endtoend/net/list',
    method: 'get',
    params: query
  })
}

// 查询网络拨测详细
export function getNet(id) {
  return request({
    url: '/probe/endtoend/net/' + id,
    method: 'get'
  })
}

// 新增网络拨测
export function addNet(data) {
  return request({
    url: '/probe/endtoend/net',
    method: 'post',
    data: data
  })
}

// 修改网络拨测
export function updateNet(data) {
  return request({
    url: '/probe/endtoend/net',
    method: 'put',
    data: data
  })
}

// 删除网络拨测
export function delNet(id) {
  return request({
    url: '/probe/endtoend/net/' + id,
    method: 'delete'
  })
}

// 启用暂停
export function suspendNet(id, suspend) {
  return request({
    url: '/probe/endtoend/net/suspend/' + id + '?suspend=' + suspend,
    method: 'post',
  })
}

export function getOptions() {
  return request({
    url: '/monitor/businessaccess/options',
    method: 'get',
  })
}

export function getAccessOptions() {
  return request({
    url: '/monitor/businessaccess/access/list',
    method: 'get',
  })
}