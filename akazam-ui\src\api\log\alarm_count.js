import request from '@/utils/request'

// 查询日志异常告警-告警规则-日志量列表
export function listAlarm(query) {
  return request({
    url: '/log/alarm/count/list',
    method: 'get',
    params: query
  })
}

// 查询日志异常告警-告警规则-日志量详细
export function getAlarm(id) {
  return request({
    url: '/log/alarm/count/' + id,
    method: 'get'
  })
}

// 新增日志异常告警-告警规则-日志量
export function addAlarm(data) {
  return request({
    url: '/log/alarm/count',
    method: 'post',
    data: data
  })
}

// 修改日志异常告警-告警规则-日志量
export function updateAlarm(data) {
  return request({
    url: '/log/alarm/count',
    method: 'put',
    data: data
  })
}

// 删除日志异常告警-告警规则-日志量
export function delAlarm(id) {
  return request({
    url: '/log/alarm/count/' + id,
    method: 'delete'
  })
}

// 根据告警类别查询子字典
export function listAlarmType(query) {
  return request({
    url: '/log/alarm/count/alarm-type/' + query.id,
    method: 'get',
  })
}
