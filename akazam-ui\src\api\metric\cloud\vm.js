import request from '@/utils/request'

// 查询云主机指标列表
export function listCloudVm(query) {
  return request({
    url: '/metric/cloud/vm/list',
    method: 'get',
    params: query
  })
}

// 查询云主机指标详细
export function getCloudVm(id) {
  return request({
    url: '/metric/cloud/vm/' + id,
    method: 'get'
  })
}

// 新增云主机指标
export function addCloudVm(data) {
  return request({
    url: '/metric/cloud/vm',
    method: 'post',
    data: data
  })
}

// 修改云主机指标
export function updateCloudVm(data) {
  return request({
    url: '/metric/cloud/vm',
    method: 'put',
    data: data
  })
}

// 删除云主机指标
export function delCloudVm(id) {
  return request({
    url: '/metric/cloud/vm/' + id,
    method: 'delete'
  })
}
