import request from '@/utils/request'

// 查询日志异常告警-告警管理列表
export function listManager(query) {
  return request({
    url: '/log/alarm/manager/list',
    method: 'get',
    params: query
  })
}

// 查询日志异常告警-告警管理详细
export function getManager(id) {
  return request({
    url: '/log/alarm/manager/' + id,
    method: 'get'
  })
}

// 新增日志异常告警-告警管理
export function addManager(data) {
  return request({
    url: '/log/alarm/manager',
    method: 'post',
    data: data
  })
}

// 修改日志异常告警-告警管理
export function updateManager(data) {
  return request({
    url: '/log/alarm/manager',
    method: 'put',
    data: data
  })
}

// 删除日志异常告警-告警管理
export function delManager(id) {
  return request({
    url: '/log/alarm/manager/' + id,
    method: 'delete'
  })
}
