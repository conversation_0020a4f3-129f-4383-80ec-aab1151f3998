import request from '@/utils/request'

// 查询WAF监测告警记录列表
export function listHistory(query) {
  return request({
    url: '/alarm/history/list',
    method: 'get',
    params: query
  })
}

// 查询WAF监测告警记录详细
export function getHistory(id) {
  return request({
    url: '/alarm/history/' + id,
    method: 'get'
  })
}

// 新增WAF监测告警记录
export function addHistory(data) {
  return request({
    url: '/alarm/history',
    method: 'post',
    data: data
  })
}

// 修改WAF监测告警记录
export function updateHistory(data) {
  return request({
    url: '/alarm/history',
    method: 'put',
    data: data
  })
}

// 删除WAF监测告警记录
export function delHistory(id) {
  return request({
    url: '/alarm/history/' + id,
    method: 'delete'
  })
}
