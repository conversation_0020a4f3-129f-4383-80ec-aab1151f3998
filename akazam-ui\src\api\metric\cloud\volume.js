import request from '@/utils/request'

// 查询云硬盘指标列表
export function listCloudVolume(query) {
  return request({
    url: '/metric/cloud/volume/list',
    method: 'get',
    params: query
  })
}

// 查询云硬盘指标详细
export function getCloudVolume(id) {
  return request({
    url: '/metric/cloud/volume/' + id,
    method: 'get'
  })
}

// 新增云硬盘指标
export function addCloudVolume(data) {
  return request({
    url: '/metric/cloud/volume',
    method: 'post',
    data: data
  })
}

// 修改云硬盘指标
export function updateCloudVolume(data) {
  return request({
    url: '/metric/cloud/volume',
    method: 'put',
    data: data
  })
}

// 删除云硬盘指标
export function delCloudVolume(id) {
  return request({
    url: '/metric/cloud/volume/' + id,
    method: 'delete'
  })
}
