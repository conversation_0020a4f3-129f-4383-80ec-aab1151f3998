import request from '@/utils/request'

// 查询授权列表
export function listAuth(query) {
  return request({
    url: '/automate/auth/list',
    method: 'get',
    params: query || { pageSize: 9999 }
  })
}

// 查询授权详细
export function getAuth(id) {
  return request({
    url: '/automate/auth/' + id,
    method: 'get'
  })
}

// 新增授权
export function addAuth(data) {
  return request({
    url: '/automate/auth',
    method: 'post',
    data: data
  })
}

// 修改授权
export function updateAuth(data) {
  return request({
    url: '/automate/auth',
    method: 'put',
    data: data
  })
}

// 删除授权
export function delAuth(id) {
  return request({
    url: '/automate/auth/' + id,
    method: 'delete'
  })
}

// 获取授权码
export function getAuthCode(query) {
  return request({
    url: '/automate/auth/getAuthCode',
    method: 'get',
    params: query
  })
}

// 校验授权码
export function verifyAuthCode(query) {
  return request({
    url: '/automate/auth/verifyAuthCode',
    method: 'get',
    params: query
  })
}