import request from '@/utils/request'

// 查询全链路探测列表
export function listFullLink(query) {
  return request({
    url: '/fault/fullLink/list',
    method: 'get',
    params: query
  })
}

// 查询全链路探测详细
export function getFullLink(id) {
  return request({
    url: '/fault/fullLink/' + id,
    method: 'get'
  })
}

// 新增全链路探测
export function addFullLink(data) {
  return request({
    url: '/fault/fullLink',
    method: 'post',
    data: data
  })
}

// 修改全链路探测
export function updateFullLink(data) {
  return request({
    url: '/fault/fullLink',
    method: 'put',
    data: data
  })
}

// 删除全链路探测
export function delFullLink(id) {
  return request({
    url: '/fault/fullLink/' + id,
    method: 'delete'
  })
}
