import request from '@/utils/request'
import { timestamp } from "@vueuse/core";

export function formatDate(timestamp) {
  const date = new Date(timestamp);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}


// 查询告警列表
export function listInfo(query) {
  return request({
    url: '/alarm/info/list',
    method: 'get',
    params: query
  })
}

// 查询告警详细
export function getInfo(id) {
  return request({
    url: '/alarm/info/' + id,
    method: 'get'
  })
}

// 新增告警
export function addInfo(data) {
  return request({
    url: '/alarm/info',
    method: 'post',
    data: data
  })
}

// 修改告警
export function updateInfo(data) {
  return request({
    url: '/alarm/info',
    method: 'put',
    data: data
  })
}

// 删除告警
export function delInfo(id) {
  return request({
    url: '/alarm/info/' + id,
    method: 'delete'
  })
}


// 查询告警报告
export function getReport(id, devjson) {
  return request({
    url: '/alarm/info/analysis/' + id,
    method: 'get',
    timeout: 300000,
    devjson: devjson
  })
}

// 查询告警聊天分析数据
export function getChatAnalysis(query) {
  return request({
    url: '/alarm/info/chatAnalysis',
    method: 'get',
    params: query
  })
}

// 查询关联分析数据
export function getRelationAnalysis(alarmSource, devjson) {
  return request({
    url: '/alarm/info/relationAnalysis/' + alarmSource,
    method: 'get',
    timeout: 300000,
    devjson: devjson
  })
}

// 查询AI运维助手预设问题对应的告警数据
export function getQuestionAlarmList(questionType) {
  return request({
    url: '/alarm/info/question/' + questionType,
    method: 'get'
  })
}

// 查询告警数量
export function getAlarmCount() {
  return request({
    url: '/alarm/info/count',
    method: 'get'
  })
}
