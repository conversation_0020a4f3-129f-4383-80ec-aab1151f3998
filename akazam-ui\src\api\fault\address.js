import request from '@/utils/request'

// 查询云主机变量配置列表
export function listAddress(query) {
  return request({
    url: '/fault/address/list',
    method: 'get',
    params: query
  })
}

// 查询云主机变量配置详细
export function getAddress(id) {
  return request({
    url: '/fault/address/' + id,
    method: 'get'
  })
}

// 新增云主机变量配置
export function addAddress(data) {
  return request({
    url: '/fault/address',
    method: 'post',
    data: data
  })
}

// 修改云主机变量配置
export function updateAddress(data) {
  return request({
    url: '/fault/address',
    method: 'put',
    data: data
  })
}

// 删除云主机变量配置
export function delAddress(id) {
  return request({
    url: '/fault/address/' + id,
    method: 'delete'
  })
}
