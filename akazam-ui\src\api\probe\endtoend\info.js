import request from '@/utils/request'

// 查询告警信息列表
export function listInfo(query) {
  return request({
    url: '/probe/endtoend/alarm/info/list',
    method: 'get',
    params: query
  })
}

// 查询告警信息详细
export function getInfo(id) {
  return request({
    url: '/probe/endtoend/alarm/info/' + id,
    method: 'get'
  })
}

// 新增告警信息
export function addInfo(data) {
  return request({
    url: '/probe/endtoend/alarm/info',
    method: 'post',
    data: data
  })
}

// 修改告警信息
export function updateInfo(data) {
  return request({
    url: '/probe/endtoend/alarm/info',
    method: 'put',
    data: data
  })
}

// 删除告警信息
export function delInfo(id, createTime) {
  return request({
    url: '/probe/endtoend/alarm/info/' + id + '?date=' + createTime,
    method: 'delete'
  })
}
