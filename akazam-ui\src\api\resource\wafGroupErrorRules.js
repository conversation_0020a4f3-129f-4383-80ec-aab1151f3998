import request from '@/utils/request'

export function getList(query) { // 分页查询WAF群障告警规则 #
  return request({
    url: '/alarm/obstacle/list',
    method: 'get',
    params: query
  })
}

export function getTemplateOptions() { // 查询告警模板 #
  return request({
    url: '/alarm/obstacle/template-options',
    method: 'get'
  })
}
export function getSmsOptions() { // 查询短信模板 #
  return request({
    url: '/alarm/obstacle/sms-options',
    method: 'get'
  })
}
export function getClusterOptions() { // 查询集群列表 #
  return request({
    url: '/alarm/obstacle/cluster-options',
    method: 'get'
  })
}
export function add(data) { // 新增WAF群障告警规则 #
  return request({
    url: '/alarm/obstacle',
    method: 'post',
    data
  })
}
export function update(data) { // 编辑WAF群障告警规则 #
  return request({
    url: '/alarm/obstacle/',
    method: 'put',
    data
  })
}
export function deleteObstacle (id) { // 删除WAF群障告警规则 #
  return request({
    url: `/alarm/obstacle/${id}`,
    method: 'delete'
  })
}
export function getDetail(id) { // 查询WAF群障规则详情 #
  return request({
    url: `/alarm/obstacle/${id}`,
    method: 'get'
  })
}