import request from '@/utils/request'

// 查询宿主机指标列表
export function listHost(query) {
  return request({
    url: '/metric/host/list',
    method: 'get',
    params: query
  })
}

// 查询宿主机指标详细
export function getHost(id) {
  return request({
    url: '/metric/host/' + id,
    method: 'get'
  })
}

// 新增宿主机指标
export function addHost(data) {
  return request({
    url: '/metric/host',
    method: 'post',
    data: data
  })
}

// 修改宿主机指标
export function updateHost(data) {
  return request({
    url: '/metric/host',
    method: 'put',
    data: data
  })
}

// 删除宿主机指标
export function delHost(id) {
  return request({
    url: '/metric/host/' + id,
    method: 'delete'
  })
}
