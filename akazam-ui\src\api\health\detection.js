import request from '@/utils/request'

// 查询健康度检测列表
export function listDetection(query) {
  return request({
    url: '/health/detection/list',
    method: 'get',
    params: query
  })
}

// 查询健康度检测详细
export function getDetection(id) {
  return request({
    url: '/health/detection/' + id,
    method: 'get'
  })
}

// 新增健康度检测
export function addDetection(data) {
  return request({
    url: '/health/detection',
    method: 'post',
    data: data
  })
}

// 修改健康度检测
export function updateDetection(data) {
  return request({
    url: '/health/detection',
    method: 'put',
    data: data
  })
}

// 删除健康度检测
export function delDetection(id) {
  return request({
    url: '/health/detection/' + id,
    method: 'delete'
  })
}
