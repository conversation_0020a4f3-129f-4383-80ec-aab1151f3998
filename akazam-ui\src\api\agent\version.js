import request from '@/utils/request'
import {ElLoading, ElMessage} from "element-plus";
import {blobValidate} from "@/utils/akazam";

// 查询探针子系统-版本列表
export function listVersion(query) {
  return request({
    url: '/agent/version/list',
    method: 'get',
    params: query
  })
}

// 查询探针子系统-版本详细
export function getVersion(id) {
  return request({
    url: '/agent/version/' + id,
    method: 'get'
  })
}

export function rollback(id) {
  return request({
    url: '/agent/version/rollback/' + id,
    method: 'get'
  })
}

export function download(id) {
  const url = '/agent/version/download/' + id;
  // TODO 下载文件

}


// 新增探针子系统-版本
export function addVersion(data) {
  return request({
    url: '/agent/version',
    method: 'post',
    data: data
  })
}

// 修改探针子系统-版本
export function updateVersion(data) {
  return request({
    url: '/agent/version',
    method: 'put',
    data: data
  })
}

// 删除探针子系统-版本
export function delVersion(id) {
  return request({
    url: '/agent/version/' + id,
    method: 'delete'
  })
}

// 新增探针子系统-版本
export function versionUpload(data) {
  return request({
    url: '/agent/version/upload',
    method: 'post',
    data: data,
    timeout: 600000
  })
}