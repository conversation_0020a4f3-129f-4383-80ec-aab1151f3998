---
description: 
globs: 
alwaysApply: false
---
# API 调用指南

Akazam 平台使用 Axios 进行 API 调用，采用模块化方式组织 API 接口。

## API 目录结构

API 接口按功能模块组织在 `src/api` 目录下：

- `src/api/login.js` - 登录相关接口
- `src/api/menu.js` - 菜单相关接口
- `src/api/dify.js` - Dify AI 相关接口
- `src/api/system/` - 系统管理相关接口
- `src/api/automate/` - 自动化相关接口
- `src/api/monitor/` - 监控相关接口
- `src/api/alarm/` - 告警相关接口
- `src/api/resource/` - 资源相关接口
- `src/api/log/` - 日志相关接口
- `src/api/topology/` - 拓扑图相关接口

## API 调用规范

1. 使用封装的 Axios 实例进行 API 调用
2. API 函数应当返回 Promise 对象
3. 使用 RESTful 风格设计 API
4. 统一处理错误和响应

## API 调用示例

```javascript
// 导入封装的 API 函数
import { listUser, getUser, addUser, updateUser, delUser } from '@/api/system/user';

// 使用 API 函数
listUser(queryParams).then(response => {
  // 处理响应
}).catch(error => {
  // 处理错误
});
```

## 响应格式

API 响应统一为以下格式：

```javascript
{
  code: 200,        // 状态码，200 表示成功
  msg: "success",   // 状态消息
  data: {},         // 响应数据
  total: 10         // 总记录数（分页查询时使用）
}
```

## 错误处理

API 调用错误由统一的拦截器处理，位于 `src/utils/request.js`。常见错误码：

- 401: 未授权，需要登录
- 403: 禁止访问，权限不足
- 404: 资源不存在
- 500: 服务器内部错误

