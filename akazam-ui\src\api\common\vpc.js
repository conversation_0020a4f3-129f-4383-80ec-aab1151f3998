import request from '@/utils/request'

// 查询VPC列表
export function listVpc(query) {
  return request({
    url: '/common/vpc/list',
    method: 'get',
    params: query
  })
}

// 查询VPC详细
export function getVpc(id) {
  return request({
    url: '/common/vpc/' + id,
    method: 'get'
  })
}

// 新增VPC
export function addVpc(data) {
  return request({
    url: '/common/vpc',
    method: 'post',
    data: data
  })
}

// 修改VPC
export function updateVpc(data) {
  return request({
    url: '/common/vpc',
    method: 'put',
    data: data
  })
}

// 删除VPC
export function delVpc(id) {
  return request({
    url: '/common/vpc/' + id,
    method: 'delete'
  })
}
