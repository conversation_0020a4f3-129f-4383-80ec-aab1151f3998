import request from '@/utils/request'

// 查询作业列表
export function listJob(query) {
  return request({
    url: '/automate/job/list',
    method: 'get',
    params: query
  })
}

// 查询作业详细
export function getJob(id) {
  return request({
    url: '/automate/job/' + id,
    method: 'get'
  })
}

// 新增作业
export function addJob(data) {
  return request({
    url: '/automate/job',
    method: 'post',
    data: data
  })
}

// 修改作业
export function updateJob(data) {
  return request({
    url: '/automate/job',
    method: 'put',
    data: data
  })
}

export function copyJob(data) {
  return request({
    url: '/automate/job/clone',
    method: 'post',
    data: data
  })
}

// 删除作业
export function delJob(id) {
  return request({
    url: '/automate/job/' + id,
    method: 'delete'
  })
}


// 启动、暂停作业
export function changeActive(query) {
  return request({
    url: '/automate/job/changeActive',
    method: 'get',
    params: query
  })
}


export function getLogicData(chainName) {
  return request({
    url: '/automate/flow/' + chainName,
    method: 'get'
  })
}

export function saveFlowData(data) {
  return request({
    url: '/automate/flow',
    method: 'post',
    data: data
  })
}

export function runJob(jobId) {
  return request({
    url: '/automate/job/run/' + jobId,
    method: 'get',
    data: {},
  })
}


export function getTemplate(smsType) {
  return request({
    url: '/sms/template/select/' + smsType,
    method: 'get'
  })
}

export function getAlarmTemplate(smsType) {
  return request({
    url: '/alarm/obstacle/template-options',
    method: 'get'
  })
}