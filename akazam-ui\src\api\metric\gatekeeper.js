import request from '@/utils/request'

// 查询网闸指标列表
export function listGatekeeper(query) {
  return request({
    url: '/metric/gatekeeper/list',
    method: 'get',
    params: query
  })
}

// 查询网闸指标详细
export function getGatekeeper(id) {
  return request({
    url: '/metric/gatekeeper/' + id,
    method: 'get'
  })
}

// 新增网闸指标
export function addGatekeeper(data) {
  return request({
    url: '/metric/gatekeeper',
    method: 'post',
    data: data
  })
}

// 修改网闸指标
export function updateGatekeeper(data) {
  return request({
    url: '/metric/gatekeeper',
    method: 'put',
    data: data
  })
}

// 删除网闸指标
export function delGatekeeper(id) {
  return request({
    url: '/metric/gatekeeper/' + id,
    method: 'delete'
  })
}
