import request from '@/utils/request'

// 查询http拨测列表
export function listHttp(query) {
  return request({
    url: '/probe/endtoend/http/list',
    method: 'get',
    params: query
  })
}

// 查询http拨测详细
export function getHttp(id) {
  return request({
    url: '/probe/endtoend/http/' + id,
    method: 'get'
  })
}

// 新增http拨测
export function addHttp(data) {
  return request({
    url: '/probe/endtoend/http',
    method: 'post',
    data: data
  })
}

// 修改http拨测
export function updateHttp(data) {
  return request({
    url: '/probe/endtoend/http',
    method: 'put',
    data: data
  })
}

// 删除http拨测
export function delHttp(id) {
  return request({
    url: '/probe/endtoend/http/' + id,
    method: 'delete'
  })
}

// 启用暂停
export function suspendHttp(id, suspend) {
  return request({
    url: '/probe/endtoend/http/suspend/' + id + '?suspend=' + suspend,
    method: 'post',
  })
}

