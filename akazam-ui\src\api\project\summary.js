import request from '@/utils/request'

// 查询项目概况列表
export function listSummary(query) {
  return request({
    url: '/project/summary/list',
    method: 'get',
    params: query
  })
}

// 查询项目概况详细
export function getSummary(id) {
  return request({
    url: '/project/summary/' + id,
    method: 'get'
  })
}

// 新增项目概况
export function addSummary(data) {
  return request({
    url: '/project/summary',
    method: 'post',
    data: data
  })
}

// 修改项目概况
export function updateSummary(data) {
  return request({
    url: '/project/summary',
    method: 'put',
    data: data
  })
}

// 删除项目概况
export function delSummary(id) {
  return request({
    url: '/project/summary/' + id,
    method: 'delete'
  })
}
