import request from '@/utils/request'

// 查询日志分析-聚类历史列表
export function listHistory(query) {
  return request({
    url: '/log/analysis/history/list',
    method: 'get',
    params: query
  })
}

// 查询日志分析-聚类历史详细
export function getHistory(id) {
  return request({
    url: '/log/analysis/history/' + id,
    method: 'get'
  })
}

// 删除日志分析-聚类历史
export function delHistory(id) {
  return request({
    url: '/log/analysis/history/' + id,
    method: 'delete'
  })
}
