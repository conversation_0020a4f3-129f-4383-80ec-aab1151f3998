import request from '@/utils/request'

// 查询统计数据
export function getOverviewInfo(query) {
  return request({
    url: '/automate/overview/info',
    method: 'get',
    params: query,
  })
}

// 查询作业计划总览
export function getJobOverview(query) {
  return request({
    url: '/automate/overview/getJobOverview',
    method: 'get',
    params: query,
  })
}

export function getTendency(query) {
  return request({
    url: '/automate/overview/getTendency',
    method: 'get',
    params: query,
  })
}

export function getMetricCount(query) {
  return request({
    url: '/automate/overview/getMetricCount',
    method: 'get',
    params: query,
  })
}

export function getTemplateCount(query) {
  return request({
    url: '/automate/overview/getTemplateCount',
    method: 'get',
    params: query,
  })
}

export function getDeviceCount(query) {
  return request({
    url: '/automate/overview/getDeviceCount',
    method: 'get',
    params: query,
  })
}

export function getAuthCount(query) {
  return request({
    url: '/automate/overview/getAuthCount',
    method: 'get',
    params: query,
  })
}
