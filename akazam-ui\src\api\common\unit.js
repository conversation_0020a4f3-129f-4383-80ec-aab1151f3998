import request from '@/utils/request'

// 查询预算单位列表
export function listUnit(query) {
  return request({
    url: '/common/unit/list',
    method: 'get',
    params: query
  })
}

// 查询预算单位详细
export function getUnit(id) {
  return request({
    url: '/common/unit/' + id,
    method: 'get'
  })
}

// 新增预算单位
export function addUnit(data) {
  return request({
    url: '/common/unit',
    method: 'post',
    data: data
  })
}

// 修改预算单位
export function updateUnit(data) {
  return request({
    url: '/common/unit',
    method: 'put',
    data: data
  })
}

// 删除预算单位
export function delUnit(id) {
  return request({
    url: '/common/unit/' + id,
    method: 'delete'
  })
}
