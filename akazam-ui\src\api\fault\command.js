import request from '@/utils/request'

// 查询故障探测-指令集列表
export function listCommand(query) {
  return request({
    url: '/fault/command/list',
    method: 'get',
    params: query
  })
}

// 查询故障探测-指令集详细
export function getCommand(id) {
  return request({
    url: '/fault/command/' + id,
    method: 'get'
  })
}

// 新增故障探测-指令集
export function addCommand(data) {
  return request({
    url: '/fault/command',
    method: 'post',
    data: data
  })
}

// 修改故障探测-指令集
export function updateCommand(data) {
  return request({
    url: '/fault/command',
    method: 'put',
    data: data
  })
}

// 删除故障探测-指令集
export function delCommand(id) {
  return request({
    url: '/fault/command/' + id,
    method: 'delete'
  })
}
