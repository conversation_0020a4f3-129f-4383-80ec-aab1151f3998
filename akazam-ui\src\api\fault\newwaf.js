import request from '@/utils/request'

// 新增探测规则
export function addDetectionRule(data) {
  return request({
    url: '/resource/rule',
    method: 'post',
    data
  })
}

// 修改探测规则
export function updateDetectionRule(data) {
  return request({
    url: '/resource/rule',
    method: 'put',
    data
  })
}

// 查询探测规则详情
export function getDetectionRule(id) {
  return request({
    url: `/resource/rule/${id}`,
    method: 'get'
  })
}

// 查询WAF集群选择框
export function getWafCluster() {
  return request({
    url: '/resource/rule/clusters',
    method: 'get'
  })
}

// 查询探针选择框
export function getProbe() {
  return request({
    url: '/resource/rule/probes',
    method: 'get'
  })
}

// 根据集群ID查询站点选择框
export function getSite(clusterId) {
  return request({
    url: `/resource/rule/sites/${clusterId}`,
    method: 'get'
  })
}

export function getPageSite(query) {
  return request({
    url: `/resource/rule/page-site`,
    method: 'get',
    params: query
  })
}



// 删除探测规则
export function deleteTaskById(id) {
  return request({
    url: `/resource/rule/${id}`,
    method: 'delete'
  })
}

// 分页查询探测规则列表
export function getTaskList(query) {
  return request({
    url: '/resource/rule/list',
    method: 'get',
    params: query
  })
}

// 查询探测点
export function getSitesBinding(ruleId) {
  return request({
    url: `/resource/rule/sites-binding/${ruleId}`,
    method: 'get'
  })
}


// 
export function getOverview(query, devjson) {
  return request({
    url: '/fault/waf-detect/overview',
    method: 'get',
    params: query,
    devjson: devjson
  })
}


// 查询集群详情
export function getClusterDetail(query) {
  return request({
    url: `/fault/waf-detect/cluster-detail`,
    method: 'get',
    params: query
  })
}

// 查询站点详情
export function getSiteDetail(query) {
  return request({
    url: `/fault/waf-detect/site-detail`,
    method: 'get',
    params: query
  })
}


// 查询监控分析
export function getMonitorAnalysis(query) {
  return request({
    url: `/fault/waf-detect/monitor-analysis`,
    method: 'get',
    params: query
  })
}

// 获取集群选项
export function getClusterOptions() {
    return request({
        url: '/fault/waf-detect/cluster-options',
        method: 'get'
    })
}

export function getSiteOptions(parentId) {
  return request({
      url: `/fault/waf-detect/site-options/${parentId}`,
      method: 'get'
  })
}

// 获取集群站点列表
export function getSiteList(query) {
  return request({
    url: '/fault/waf-detect/overview/list',
    method: 'get',
    params: query
  })
}

// 获取集群站点列表
export function getClusterSiteList(query) {
  return request({
    url: '/fault/waf-detect/cluster-detail-list',
    method: 'get',
    params: query
  })
}

// 获取集群站点异常记录列表
export function getClusterSiteExceptionRecordList(query) {
  return request({
    url: '/fault/waf-detect/cluster-detail-exception-record-list',
    method: 'get',
    params: query
  })
}