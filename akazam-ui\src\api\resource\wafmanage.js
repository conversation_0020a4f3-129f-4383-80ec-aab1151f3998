import request from '@/utils/request'

// 查询节点列表
export function listNode(query) {
  return request({
    url: '/resource/nodes/list',
    method: 'get',
    params: query
  })
}

// 集群总览
export function clusterOverview(query) {
  return request({
    url: '/resource/cluster/overview',
    method: 'get',
    params: query
  })
}

// 新增集群
export function addCluster(data) {
  return request({
    url: '/resource/cluster',
    method: 'post',
    data: data
  })
}

// 删除集群
export function deleteCluster(clusterId) {
  return request({
    url: `/resource/cluster/${clusterId}`,
    method: 'delete'
  })
}

// 获取集群详情
export function getClusterDetail(clusterId) {
  return request({
    url: `/resource/cluster/${clusterId}`,
    method: 'get'
  })
}

// 更新集群
export function updateCluster(data) {
  return request({
    url: '/resource/cluster',
    method: 'put',
    data: data
  })
}

// 导入模板
export function importTemplate(data) {
  return request({
    url: '/resource/site/importTemplate',
    method: 'post',
    data: data
  })
}

// 导入站点
export function importSite(data) {
  return request({
    url: '/resource/site/import',
    method: 'post',
    data: data
  })
}

// 集群新增中查询备选节点
export function alternativeNodes(query = {}) {
  return request({
    url: `/resource/nodes/alternativeNodes/${query.clusterId || -1}`,
    method: 'get',
    params: query
  })
}

// 站点列表
export function listSite(query) {
  return request({
    url: '/resource/site/list',
    method: 'get',
    params: query
  })
}

// 节点详细信息
export function getNodeInfoDetail(id) {
  return request({
    url: `/resource/nodes/info/${id}`,
    method: 'get'
  })
}

// 更新节点信息
export function updateNodeInfo(data) {
  return request({
    url: '/resource/nodes',
    method: 'put',
    data: data
  })
}

// 修改站点
    
export function updateSite(data) {
  return request({
    url: '/resource/site',
    method: 'put',
    data: data
  })
}

// 删除站点
export function deleteSite(ids) {
  return request({
    url: `/resource/site/${ids}`,
    method: 'delete'
  })
}

// 新增保护站点
export function addSite(data) {
  return request({
    url: '/resource/site',
    method: 'post',
    data: data
  })
}

// 获取机房列表
export function listRooms() {
  return request({
    url: '/resource/cluster/rooms',
    method: 'get'
  })
}

// 根据机房ID获取区域列表
export function listZonesByRoom(roomId) {
  return request({
    url: `/resource/cluster/zones/${roomId}`,
    method: 'get'
  })
}

