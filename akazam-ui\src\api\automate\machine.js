import request from '@/utils/request'

// 模拟获取机器列表
export function getMachineList() {
  return new Promise((r) =>
    r({
      msg: '操作成功',
      code: 200,
      rows: [
        {
          name: 'XXY15_G_PEPL_CE68_R01_U43_1',
          id: '092176A481EA31509BB63E568C483F3A',
        },
        {
          name: 'XXY15_G_PEPL_CE68_R02_U43_2',
          id: '55AAD182C9573680B363376C2C9036A5',
        }
      ],
    }),
  );
}


export function getTerminalAccess(query) {
  return request({
    url: '/terminal/access',
    method: 'post',
    data: query
  })
}