import request from '@/utils/request'

// 查询区域列表
export function listZone(query) {
  return request({
    url: '/resource/zone/list',
    method: 'get',
    params: query
  })
}

// 查询区域详细
export function getZone(id) {
  return request({
    url: '/resource/zone/' + id,
    method: 'get'
  })
}

// 新增区域
export function addZone(data) {
  return request({
    url: '/resource/zone',
    method: 'post',
    data: data
  })
}

// 修改区域
export function updateZone(data) {
  return request({
    url: '/resource/zone',
    method: 'put',
    data: data
  })
}

// 删除区域
export function delZone(id) {
  return request({
    url: '/resource/zone/' + id,
    method: 'delete'
  })
}
