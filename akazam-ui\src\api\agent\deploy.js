import request from '@/utils/request'

// 查询探针子系统-远程部署列表
export function listDeploy(query) {
  return request({
    url: '/agent/deploy/list',
    method: 'get',
    params: query
  })
}

// 查询探针子系统-远程部署详细
export function getDeploy(id) {
  return request({
    url: '/agent/deploy/' + id,
    method: 'get'
  })
}

export function getHeart(id) {
  return request({
    url: '/agent/deploy/heartBeat/' + id,
    method: 'get'
  })
}

export function refresh(id) {
  return request({
    url: '/agent/deploy/refresh/' + id,
    method: 'get'
  })
}

// 新增探针子系统-远程部署
export function addDeploy(data) {
  return request({
    url: '/agent/deploy',
    method: 'post',
    data: data
  })
}

// 修改探针子系统-远程部署
export function updateDeploy(data) {
  return request({
    url: '/agent/deploy',
    method: 'put',
    data: data
  })
}

// 删除探针子系统-远程部署
export function delDeploy(id) {
  return request({
    url: '/agent/deploy/' + id,
    method: 'delete'
  })
}
