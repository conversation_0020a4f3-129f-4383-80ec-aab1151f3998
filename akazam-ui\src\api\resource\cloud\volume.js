import request from '@/utils/request'

// 查询云硬盘列表
export function listCloudVolume(query) {
  return request({
    url: '/resource/cloud/volume/list',
    method: 'get',
    params: query
  })
}

// 查询云硬盘详细
export function getCloudVolume(id) {
  return request({
    url: '/resource/cloud/volume/' + id,
    method: 'get'
  })
}

// 新增云硬盘
export function addCloudVolume(data) {
  return request({
    url: '/resource/cloud/volume',
    method: 'post',
    data: data
  })
}

// 修改云硬盘
export function updateCloudVolume(data) {
  return request({
    url: '/resource/cloud/volume',
    method: 'put',
    data: data
  })
}

// 删除云硬盘
export function delCloudVolume(id) {
  return request({
    url: '/resource/cloud/volume/' + id,
    method: 'delete'
  })
}
