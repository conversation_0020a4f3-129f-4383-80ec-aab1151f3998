import request from '@/utils/request'

// 查询采集样本列表
export function listCollection(query) {
  return request({
    url: '/health/data/collection/list',
    method: 'get',
    params: query
  })
}

export function markPlusData(ids){
  return request({
    url: '/health/data/collection/marker/plus/' + ids,
    method: 'post'
  })
}

export function markMinusData(ids){
  return request({
    url: '/health/data/collection/marker/minus/' + ids,
    method: 'post'
  })
}

// 查询采集样本详细
export function getCollection(id) {
  return request({
    url: '/health/data/collection/' + id,
    method: 'get'
  })
}

// 新增采集样本
export function addCollection(data) {
  return request({
    url: '/health/data/collection',
    method: 'post',
    data: data
  })
}

// 修改采集样本
export function updateCollection(data) {
  return request({
    url: '/health/data/collection',
    method: 'put',
    data: data
  })
}

// 删除采集样本
export function delCollection(id) {
  return request({
    url: '/health/data/collection/' + id,
    method: 'delete'
  })
}
