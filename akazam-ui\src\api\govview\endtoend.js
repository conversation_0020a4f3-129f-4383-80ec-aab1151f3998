import request from "@/utils/request";

// 查询健康告警管理列表
export function getEnd2endData() {
  return request({
    url: `/monitor/end2end/overview/data`,
    method: "get",
  });
}

// 端到端探测详情接口 /monitor/end2end/probe/detail?domain=xxx.com
export function getEnd2endDetail(domain) {
  return request({
    url: `/monitor/end2end/probe/detail?domain=${domain}`,
    method: "get",
  });
}

// 端到端探测主动探测接口 /monitor/end2end/probe/detect?domain=xxx.com&probeIds=1,2,3
export function getEnd2endDetect(domain, probeIds) {
  return request({
    url: `/monitor/end2end/probe/detect?domain=${domain}&probeIds=${probeIds}`,
    method: "get",
  });
}

// 端到端探测历史接口 /monitor/end2end/probe/history?domain=xxx.com&probeId=xxx&size=10
export function getEnd2endHistory(domain, probeId, size) {
  return request({
    url: `/monitor/end2end/probe/history?domain=${domain}&probeId=${probeId}&size=${size}`,
    method: "get",
  });
}

export function getListIps(ips) {
  return request({
    url: `/monitor/end2end/snmp/ips?ips=${ips}`,
    method: "get",
  });
}

export function getListGatekeeper(ips) {
  return request({
    url: `/monitor/end2end/snmp/gatekeeper?ips=${ips}`,
    method: "get",
  });
}

export function getListFw(ips) {
  return request({
    url: `/monitor/end2end/snmp/fw?ips=${ips}`,
    method: "get",
  });
}

// 站点一键响应
export function siteResponsePost(data) {
  return request({
    url: "/monitor/end2end/alarm/response",
    method: "post",
    data: data,
  });
}

// 端到端右侧告警响应 PUT /alarm/business/threshold/response/{{ids}}
export function siteResponsePut(ids) {
  return request({
    url: `/alarm/business/threshold/response/${ids}`,
    method: "put",
  });
}
