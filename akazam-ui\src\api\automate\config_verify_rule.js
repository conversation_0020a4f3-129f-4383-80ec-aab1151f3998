import request from '@/utils/request'

// 查询配置校验规则列表
export function listConfigVerifyRule(query) {
  return request({
    url: '/automate/config/verify/rule/list',
    method: 'get',
    params: query
  })
}

// 查询配置校验规则详细
export function getConfigVerifyRule(id) {
  return request({
    url: '/automate/config/verify/rule/' + id,
    method: 'get'
  })
}

// 新增配置校验规则
export function addConfigVerifyRule(data) {
  return request({
    url: '/automate/config/verify/rule',
    method: 'post',
    data: data
  })
}

// 修改配置校验规则
export function updateConfigVerifyRule(data) {
  return request({
    url: '/automate/config/verify/rule',
    method: 'put',
    data: data
  })
}

// 删除配置校验规则
export function delConfigVerifyRule(id) {
  return request({
    url: '/automate/config/verify/rule/' + id,
    method: 'delete'
  })
}

export function getConfigVerifyRuleSelection() {
  return request({
    url: '/automate/config/verify/rule/selection',
    method: 'get'
  })
}
