import request from '@/utils/request'

// 查询预算单位配额信息列表
export function listUnitQuota(query) {
  return request({
    url: '/common/unit/quota/list',
    method: 'get',
    params: query
  })
}

// 查询预算单位配额信息详细
export function getUnitQuota(id) {
  return request({
    url: '/common/unit/quota/' + id,
    method: 'get'
  })
}

// 新增预算单位配额信息
export function addUnitQuota(data) {
  return request({
    url: '/common/unit/quota',
    method: 'post',
    data: data
  })
}

// 修改预算单位配额信息
export function updateUnitQuota(data) {
  return request({
    url: '/common/unit/quota',
    method: 'put',
    data: data
  })
}

// 删除预算单位配额信息
export function delUnitQuota(id) {
  return request({
    url: '/common/unit/quota/' + id,
    method: 'delete'
  })
}
