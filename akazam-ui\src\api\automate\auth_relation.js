import request from '@/utils/request'

// 查询授权用户关系列表
export function listAuth_relation(query) {
  return request({
    url: '/automate/auth_relation/list',
    method: 'get',
    params: query
  })
}

// 查询授权用户关系详细
export function getAuth_relation(id) {
  return request({
    url: '/automate/auth_relation/' + id,
    method: 'get'
  })
}

// 新增授权用户关系
export function addAuth_relation(data) {
  return request({
    url: '/automate/auth_relation',
    method: 'post',
    data: data
  })
}

// 修改授权用户关系
export function updateAuth_relation(data) {
  return request({
    url: '/automate/auth_relation',
    method: 'put',
    data: data
  })
}

// 删除授权用户关系
export function delAuth_relation(id) {
  return request({
    url: '/automate/auth_relation/' + id,
    method: 'delete'
  })
}
