import request from '@/utils/request'

// 查询health列表
export function list(query) {
  return request({
    url: '/health/follow/list',
    method: 'get',
    params: query
  })
}

// 查询health详细
export function get(id) {
  return request({
    url: '/health/follow/' + id,
    method: 'get'
  })
}

// 新增health
export function addList(data) {
  return request({
    url: '/health/follow',
    method: 'post',
    data: data
  })
}

// 修改health
export function updateList(data) {
  return request({
    url: '/health/follow',
    method: 'put',
    data: data
  })
}

// 删除health
export function delList(id) {
  return request({
    url: '/health/follow/' + id,
    method: 'delete'
  })
}
