import request from '@/utils/request'

// 查询配置校验规则列表
export function listFileVerifyRule(query) {
  return request({
    url: '/automate/file/verify/rule/list',
    method: 'get',
    params: query
  })
}

// 查询配置校验规则详细
export function getFileVerifyRule(id) {
  return request({
    url: '/automate/file/verify/rule/' + id,
    method: 'get'
  })
}

// 新增配置校验规则
export function addFileVerifyRule(data) {
  return request({
    url: '/automate/file/verify/rule',
    method: 'post',
    data: data
  })
}

// 修改配置校验规则
export function updateFileVerifyRule(data) {
  return request({
    url: '/automate/file/verify/rule',
    method: 'put',
    data: data
  })
}

// 删除配置校验规则
export function delFileVerifyRule(id) {
  return request({
    url: '/automate/file/verify/rule/' + id,
    method: 'delete'
  })
}

// 下拉选项
export function getFileVerifyRuleSelection() {
  return request({
    url: '/automate/file/verify/rule/selection',
    method: 'get'
  })
}
