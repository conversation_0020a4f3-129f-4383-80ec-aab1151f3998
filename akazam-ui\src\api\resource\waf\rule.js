import request from '@/utils/request'

// 查询探测任务配置列表
export function listRule(query) {
  return request({
    url: '/resource/rule/list',
    method: 'get',
    params: query
  })
}

// 查询探测任务配置详细
export function getRule(id) {
  return request({
    url: '/resource/rule/' + id,
    method: 'get'
  })
}

// 新增探测任务配置
export function addRule(data) {
  return request({
    url: '/resource/rule',
    method: 'post',
    data: data
  })
}

// 修改探测任务配置
export function updateRule(data) {
  return request({
    url: '/resource/rule',
    method: 'put',
    data: data
  })
}

// 删除探测任务配置
export function delRule(id) {
  return request({
    url: '/resource/rule/' + id,
    method: 'delete'
  })
}
