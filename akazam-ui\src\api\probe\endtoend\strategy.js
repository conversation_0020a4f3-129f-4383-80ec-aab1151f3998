import request from '@/utils/request'

// 查询告警策略列表
export function listStrategy(query) {
  return request({
    url: '/probe/endtoend/alarm/strategy/list',
    method: 'get',
    params: query
  })
}

// 查询告警策略详细
export function getStrategy(id) {
  return request({
    url: '/probe/endtoend/alarm/strategy/' + id,
    method: 'get'
  })
}

// 新增告警策略
export function addStrategy(data) {
  return request({
    url: '/probe/endtoend/alarm/strategy',
    method: 'post',
    data: data
  })
}

// 修改告警策略
export function updateStrategy(data) {
  return request({
    url: '/probe/endtoend/alarm/strategy',
    method: 'put',
    data: data
  })
}

// 删除告警策略
export function delStrategy(id) {
  return request({
    url: '/probe/endtoend/alarm/strategy/' + id,
    method: 'delete'
  })
}

// 启用暂停
export function suspendStrategy(id, suspend) {
  return request({
    url: '/probe/endtoend/alarm/strategy/suspend/' + id + '?suspend=' + suspend,
    method: 'post',
  })
}
