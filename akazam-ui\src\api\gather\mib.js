import request from '@/utils/request'

// 查询MIB库管理列表
export function listMib(query) {
    return request({
        url: '/gather/mib/list',
        method: 'get',
        params: query
    })
}

// 查询MIB库管理详细
export function getMib(id) {
    return request({
        url: '/gather/mib/' + id,
        method: 'get'
    })
}

// 新增MIB库管理
export function addMib(data) {
    return request({
        url: '/gather/mib',
        method: 'post',
        data: data
    })
}

// 修改MIB库管理
export function updateMib(data) {
    return request({
        url: '/gather/mib',
        method: 'put',
        data: data
    })
}

// 删除MIB库管理
export function delMib(id) {
    return request({
        url: '/gather/mib/' + id,
        method: 'delete'
    })
}

export function mibUpload(data) {
    return request({
        url: '/gather/mib/upload',
        method: 'post',
        data: data,
        timeout: 600000
    })
}

export function getMibTree(id) {
    return request({
        url: '/gather/mib/tree/' + id,
        method: 'get'
    })
}

export function searchMib(queryValue, searchMode, params) {
    return request({
        url: '/gather/mib/search/' + searchMode + '/'  + queryValue,
        method: 'get',
        params: params
    })
}
