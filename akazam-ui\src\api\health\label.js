import request from '@/utils/request'

// 查询样本&样本组 标注列表
export function listMark(query) {
  return request({
    url: '/health/mark/list',
    method: 'get',
    params: query
  })
}

// 查询样本&样本组 标注详细
export function getMark(id) {
  return request({
    url: '/health/mark/' + id,
    method: 'get'
  })
}

// 新增样本&样本组 标注
export function addMark(data) {
  return request({
    url: '/health/mark',
    method: 'post',
    data: data
  })
}

// 修改样本&样本组 标注
export function updateMark(data) {
  return request({
    url: '/health/mark',
    method: 'put',
    data: data
  })
}

// 删除样本&样本组 标注
export function delMark(id) {
  return request({
    url: '/health/mark/' + id,
    method: 'delete'
  })
}


// 查询样本&样本组 标注列表
export function healthSampleLabel_info(query) {
  return request({
    url: '/health/example/label_info',
    method: 'get',
    params: query
  })
}


// 修改样本&样本组 标注
export function sampleLabel(data) {
  return request({
    url: '/health/example/label',
    method: 'put',
    data: data
  })
}
  
  
 