import request from "@/utils/request";

// 折线图
export function metricDataList(query, devjson) {
  return request({
    url: "/metric/data/list",
    method: "get",
    params: query,
    devjson: devjson ? devjson : false,
  });
}

export function logSearchFraud(query) {
  return request({
    url: "/log/search/fraud",
    method: "get",
    params: query,
  });
}

export function metricDataListsummary(query, devjson) {
  return request({
    url: "/metric/data/summary/list",
    method: "get",
    params: query,
    devjson: devjson ? devjson : false,
  });
}

// 查询指标列表
export function getMetricList(query) {
  return request({
    url: `/metric/list/${query.resourceType}`,
    method: "get",
    params: query,
  });
}

// 查询指标趋势
export function getMetricDashboard(query,devjson) {
  return request({
    url: `/metric/dashboard`,
    method: "POST",
    data: query,
    devjson: devjson ? devjson : false
  });
}
