import request from '@/utils/request'

// 查询样本组-样本关系列表
export function listFollow(query) {
  return request({
    url: '/health/follow/list',
    method: 'get',
    params: query
  })
}

// 查询样本组-样本关系详细
export function getFollow(id) {
  return request({
    url: '/health/follow/' + id,
    method: 'get'
  })
}

// 新增样本组-样本关系
export function addFollow(data) {
  return request({
    url: '/health/follow',
    method: 'post',
    data: data
  })
}

// 新增样本组-样本关系
export function saveFollowModelMetric(data) {
  return request({
    url: '/health/follow/model/metric/save',
    method: 'post',
    data: data
  })
}

export function getFollowModelMetric(id) {
  return request({
    url: '/health/follow/model/metric/get/' + id,
    method: 'get',
  })
}

// 修改样本组-样本关系
export function updateFollow(data) {
  return request({
    url: '/health/follow',
    method: 'put',
    data: data
  })
}

// 删除样本组-样本关系
export function delFollow(id) {
  return request({
    url: '/health/follow/' + id,
    method: 'delete'
  })
}
