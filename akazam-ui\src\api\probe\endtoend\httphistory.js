import request from '@/utils/request'

// 查询http信息历史记录列表
export function listHttphistory(query) {
  return request({
    url: '/probe/endtoend/httphistory/list',
    method: 'get',
    params: query
  })
}

// 查询http信息历史记录详细
export function getHttphistory(id) {
  return request({
    url: '/probe/endtoend/httphistory/' + id,
    method: 'get'
  })
}

// 新增http信息历史记录
export function addHttphistory(data) {
  return request({
    url: '/probe/endtoend/httphistory',
    method: 'post',
    data: data
  })
}

// 修改http信息历史记录
export function updateHttphistory(data) {
  return request({
    url: '/probe/endtoend/httphistory',
    method: 'put',
    data: data
  })
}

// 删除http信息历史记录
export function delHttphistory(id) {
  return request({
    url: '/probe/endtoend/httphistory/' + id,
    method: 'delete'
  })
}

// 图表
export function chartHttphistory(query) {
  return request({
    url: '/probe/endtoend/httphistory/chart',
    method: 'get',
    params: query
  })
}

// 地图
export function mapHttphistory(query) {
  return request({
    url: '/probe/endtoend/httphistory/map/overview',
    method: 'get',
    params: query
  })
}
