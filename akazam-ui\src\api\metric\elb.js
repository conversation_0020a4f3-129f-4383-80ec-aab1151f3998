import request from '@/utils/request'

// 查询负载均衡指标列表
export function listElb(query) {
  return request({
    url: '/metric/elb/list',
    method: 'get',
    params: query
  })
}

// 查询负载均衡指标详细
export function getElb(id) {
  return request({
    url: '/metric/elb/' + id,
    method: 'get'
  })
}

// 新增负载均衡指标
export function addElb(data) {
  return request({
    url: '/metric/elb',
    method: 'post',
    data: data
  })
}

// 修改负载均衡指标
export function updateElb(data) {
  return request({
    url: '/metric/elb',
    method: 'put',
    data: data
  })
}

// 删除负载均衡指标
export function delElb(id) {
  return request({
    url: '/metric/elb/' + id,
    method: 'delete'
  })
}

export function elbIp(hostip, devjson) {
  return request({
    url: `/fault/elb/detect/` + hostip,
    method: 'get',
    devjson: devjson ? devjson : null
  })
}

export function faultElb(query, devjson) {
  return request({
    url: `/fault/elb/query/${query.host}`,
    method: 'get',
    params: query,
    devjson: devjson ? devjson : null
  })
}

export function getDetail(query, devjson) {
  return request({
    url: `/monitor/businessaccess/options/${query.business}`,
    method: 'get',
    params: query,
    devjson: devjson ? devjson : null
  })
}



export function getEipOccupation(query, devjson) {
  return request({
    url: `/eip/get-eip-occupation`,
    method: 'post',
    data: query,
    devjson: devjson ? devjson : null,
    timeout: 6000000,
  })
}


