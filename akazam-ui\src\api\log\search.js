import request from "@/utils/request";

// 查询INDEX
export function logGetIndices(query) {
  return request({
    url: "/log/search/get-indices",
    method: "get",
    params: query,
  });
}

// 根据INDEX查询字段
export function logGetField(query) {
  return request({
    url: `/log/search/get-field/${query.index}`,
    method: "get",
  });
}

// 检索接口
export function logList(data) {
  return request({
    url: `log/search/list`,
    method: "post",
    data: data,
  });
}

// 检索接口
export function logStatistic(data) {
  return request({
    url: `log/search/statistic`,
    method: "post",
    data: data,
  });
}

// 详情
export function logGetDetail(query) {
  return request({
    url: `/log/search/get-detail/${query.index}/${query.id}`,
    method: "get",
  });
}

// 删除
export function logDelete(query) {
  return request({
    url: `/log/search/delete/${query.index}/${query.id}`,
    method: "delete",
  });
}

// 查询INDEX
export function logSearchCount(query, devjson) {
  return request({
    url: "/log/search/count",
    method: "get",
    params: query,
    devjson: devjson ? devjson : false
  });
}
