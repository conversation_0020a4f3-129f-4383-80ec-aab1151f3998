import request from '@/utils/request'

// 查询物理机列表
export function listStoragePhysical(query) {
  return request({
    url: '/resource/storage/physical/list',
    method: 'get',
    params: query
  })
}

// 查询物理机详细
export function getStoragePhysical(id) {
  return request({
    url: '/resource/storage/physical/' + id,
    method: 'get'
  })
}

// 新增物理机
export function addStoragePhysical(data) {
  return request({
    url: '/resource/storage/physical',
    method: 'post',
    data: data
  })
}

// 修改物理机
export function updateStoragePhysical(data) {
  return request({
    url: '/resource/storage/physical',
    method: 'put',
    data: data
  })
}

// 删除物理机
export function delStoragePhysical(id) {
  return request({
    url: '/resource/storage/physical/' + id,
    method: 'delete'
  })
}
