import request from '@/utils/request'

// 查询日志异常告警-告警规则-标签列表
export function listTag(query) {
  return request({
    url: '/log/alarm/tag/list',
    method: 'get',
    params: query
  })
}

// 查询日志异常告警-告警规则-标签详细
export function getTag(id) {
  return request({
    url: '/log/alarm/tag/' + id,
    method: 'get'
  })
}

// 新增日志异常告警-告警规则-标签
export function addTag(data) {
  return request({
    url: '/log/alarm/tag',
    method: 'post',
    data: data
  })
}

// 修改日志异常告警-告警规则-标签
export function updateTag(data) {
  return request({
    url: '/log/alarm/tag',
    method: 'put',
    data: data
  })
}

// 删除日志异常告警-告警规则-标签
export function delTag(id) {
  return request({
    url: '/log/alarm/tag/' + id,
    method: 'delete'
  })
}

// 查询标签列表
export function getTagList() {
  return request({
    url: '/log/alarm/tag/get-tag-list',
    method: 'get'
  })
}
