import request from '@/utils/request'

// 查询样本组列表
export function listGroup(query) {
  return request({
    url: '/health/s/d/g/list',
    method: 'get',
    params: query
  })
}

// 查询样本组详细
export function getGroup(id) {
  return request({
    url: '/health/s/d/g/' + id,
    method: 'get'
  })
}

// 新增样本组
export function addGroup(data) {
  return request({
    url: '/health/s/d/g',
    method: 'post',
    data: data
  })
}

// 修改样本组
export function updateGroup(data) {
  return request({
    url: '/health/s/d/g',
    method: 'put',
    data: data
  })
}

// 删除样本组
export function delGroup(id) {
  return request({
    url: '/health/s/d/g/' + id,
    method: 'delete'
  })
}

/**
 * 获取样本组下拉列表
 * @returns {*}
 */
export function selectionGroup() {
  return request({
    url: '/health/s/d/g/selection',
    method: 'get',
  })
}
