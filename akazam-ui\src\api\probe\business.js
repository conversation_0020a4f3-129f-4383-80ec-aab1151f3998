import request from '@/utils/request'

// 查询探针业务管理列表
export function listBusiness(query) {
  return request({
    url: '/probe/business/list',
    method: 'get',
    params: query
  })
}

// 查询探针业务管理详细
export function getBusiness(id) {
  return request({
    url: '/probe/business/' + id,
    method: 'get'
  })
}

// 新增探针业务管理
export function addBusiness(data) {
  return request({
    url: '/probe/business',
    method: 'post',
    data: data
  })
}

// 修改探针业务管理
export function updateBusiness(data) {
  return request({
    url: '/probe/business',
    method: 'put',
    data: data
  })
}

// 删除探针业务管理
export function delBusiness(id) {
  return request({
    url: '/probe/business/' + id,
    method: 'delete'
  })
}
