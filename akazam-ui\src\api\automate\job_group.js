import request from '@/utils/request'

// 查询作业分组列表
export function listJob_group(query) {
  return request({
    url: '/automate/job_group/list',
    method: 'get',
    params: query || { pageSize: 9999 }
  })
}

// 查询作业分组详细
export function getJob_group(id) {
  return request({
    url: '/automate/job_group/' + id,
    method: 'get'
  })
}

// 新增作业分组
export function addJob_group(data) {
  return request({
    url: '/automate/job_group',
    method: 'post',
    data: data
  })
}

// 修改作业分组
export function updateJob_group(data) {
  return request({
    url: '/automate/job_group',
    method: 'put',
    data: data
  })
}

// 删除作业分组
export function delJob_group(id) {
  return request({
    url: '/automate/job_group/' + id,
    method: 'delete'
  })
}
