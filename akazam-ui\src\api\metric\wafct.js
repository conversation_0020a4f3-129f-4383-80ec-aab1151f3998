import request from '@/utils/request'

// 查询长亭WAF指标列表
export function listWafct(query) {
  return request({
    url: '/metric/wafct/list',
    method: 'get',
    params: query
  })
}

// 查询长亭WAF指标详细
export function getWafct(id) {
  return request({
    url: '/metric/wafct/' + id,
    method: 'get'
  })
}

// 新增长亭WAF指标
export function addWafct(data) {
  return request({
    url: '/metric/wafct',
    method: 'post',
    data: data
  })
}

// 修改长亭WAF指标
export function updateWafct(data) {
  return request({
    url: '/metric/wafct',
    method: 'put',
    data: data
  })
}

// 删除长亭WAF指标
export function delWafct(id) {
  return request({
    url: '/metric/wafct/' + id,
    method: 'delete'
  })
}
