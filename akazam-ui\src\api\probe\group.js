import request from '@/utils/request'

// 查询探针业务分组列表
export function listGroup(query) {
  return request({
    url: '/probe/group/list',
    method: 'get',
    params: query
  })
}

// 查询探针业务分组详细
export function getGroup(id) {
  return request({
    url: '/probe/group/' + id,
    method: 'get'
  })
}

// 新增探针业务分组
export function addGroup(data) {
  return request({
    url: '/probe/group',
    method: 'post',
    data: data
  })
}

// 修改探针业务分组
export function updateGroup(data) {
  return request({
    url: '/probe/group',
    method: 'put',
    data: data
  })
}

// 删除探针业务分组
export function delGroup(id) {
  return request({
    url: '/probe/group/' + id,
    method: 'delete'
  })
}
