import request from '@/utils/request'

// 查询日志异常告警-告警规则-基于预测告警列表
export function listForecast(query) {
  return request({
    url: '/alarm/forecast/list',
    method: 'get',
    params: query
  })
}

// 查询日志异常告警-告警规则-基于预测告警详细
export function getForecast(id) {
  return request({
    url: '/alarm/forecast/' + id,
    method: 'get'
  })
}

// 新增日志异常告警-告警规则-基于预测告警
export function addForecast(data) {
  return request({
    url: '/alarm/forecast',
    method: 'post',
    data: data
  })
}

// 修改日志异常告警-告警规则-基于预测告警
export function updateForecast(data) {
  return request({
    url: '/alarm/forecast',
    method: 'put',
    data: data
  })
}

// 删除日志异常告警-告警规则-基于预测告警
export function delForecast(id) {
  return request({
    url: '/alarm/forecast/' + id,
    method: 'delete'
  })
}
