import request from '@/utils/request'

// 查询日志分析-标签管理列表
export function listTag(query) {
  return request({
    url: '/log/analysis/tag/list',
    method: 'get',
    params: query
  })
}

// 查询日志分析-标签管理详细
export function getTag(id) {
  return request({
    url: '/log/analysis/tag/' + id,
    method: 'get'
  })
}

// 新增日志分析-标签管理
export function addTag(data) {
  return request({
    url: '/log/analysis/tag',
    method: 'post',
    data: data
  })
}

// 修改日志分析-标签管理
export function updateTag(data) {
  return request({
    url: '/log/analysis/tag',
    method: 'put',
    data: data
  })
}

// 删除日志分析-标签管理
export function delTag(id) {
  return request({
    url: '/log/analysis/tag/' + id,
    method: 'delete'
  })
}

// 新增日志分析-标签管理
export function clusterTagAdd(data) {
  return request({
    url: '/log/analysis/cluster/tag',
    method: 'post',
    data: data
  })
}

