---
description: 
globs: 
alwaysApply: false
---
# 开发工作流指南

本指南描述了 Akazam 平台的开发工作流程和最佳实践。

## 开发环境

1. 安装依赖
   ```bash
   pnpm install
   ```

2. 启动开发服务器
   ```bash
   pnpm dev
   ```

3. 构建生产版本
   ```bash
   pnpm build:prod
   ```

## 分支管理

- `master`: 主分支，保持稳定的生产版本
- `develop`: 开发分支，包含最新的开发代码
- `feature/*`: 功能分支，用于开发新功能
- `hotfix/*`: 修复分支，用于紧急修复线上问题

## 代码风格

项目使用 ESLint 和 Prettier 进行代码格式化和检查，配置文件位于 [.eslintrc.js](mdc:.eslintrc.js)。

代码提交前请确保：

1. 代码符合 ESLint 规则
2. 组件和函数有完整的注释
3. 没有不必要的 console.log 语句
4. 没有不必要的依赖引入

## 组件开发流程

1. 分析需求，确定组件的功能和接口
2. 参考现有组件的设计模式和风格
3. 使用 Composition API 开发组件
4. 编写完整的 props 和 emits 定义
5. 确保组件的可复用性和可测试性

## 新页面开发流程

1. 在 `src/views` 目录下创建页面组件
2. 在 `src/router/index.js` 中添加路由配置
3. 如需状态管理，在 `src/stores/modules` 中添加对应模块
4. 如需 API 接口，在 `src/api` 中添加对应模块
5. 开发页面组件，遵循项目的组件设计模式

## 调试技巧

1. 使用 Vue DevTools 调试组件树和状态
2. 使用浏览器开发者工具的 Network 面板调试 API 请求
3. 使用 `console.log` 或 `debugger` 调试复杂逻辑
4. 使用 Vite 的热更新功能加速开发

## 性能优化

1. 组件懒加载：路由组件使用动态导入
2. 状态管理优化：避免过度使用全局状态
3. 使用 `v-once` 渲染静态内容
4. 使用 `v-memo` 缓存重复渲染的内容
5. 使用虚拟列表处理大数据集
6. 对大型依赖库使用按需导入

