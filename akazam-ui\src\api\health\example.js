import request from '@/utils/request'

// 查询样本管理列表
export function listExample(query) {
  return request({
    url: '/health/example/list',
    method: 'get',
    params: query
  })
}

// 查询样本管理详细
export function getExample(id) {
  return request({
    url: '/health/example/' + id,
    method: 'get'
  })
}

// 新增样本管理
export function addExample(data) {
  return request({
    url: '/health/example',
    method: 'post',
    data: data
  })
}

// 修改样本&样本组 标注
export function ExampleLabel(data) {
  return request({
    url: '/health/example/label',
    method: 'put',
    data: data
  })
}

// 修改样本管理
export function updateExample(data) {
  return request({
    url: '/health/example',
    method: 'put',
    data: data
  })
}

// 删除样本管理
export function delExample(id) {
  return request({
    url: '/health/example/' + id,
    method: 'delete'
  })
}



