import request from '@/utils/request'

// 查询事件单附加数据列表
export function listOrderAddition(query) {
  return request({
    url: '/fault/orderAddition/list',
    method: 'get',
    params: query
  })
}

// 查询事件单附加数据详细
export function getOrderAddition(id) {
  return request({
    url: '/fault/orderAddition/' + id,
    method: 'get'
  })
}

// 新增事件单附加数据
export function addOrderAddition(data) {
  return request({
    url: '/fault/orderAddition',
    method: 'post',
    data: data
  })
}

// 修改事件单附加数据
export function updateOrderAddition(data) {
  return request({
    url: '/fault/orderAddition',
    method: 'put',
    data: data
  })
}

// 删除事件单附加数据
export function delOrderAddition(id) {
  return request({
    url: '/fault/orderAddition/' + id,
    method: 'delete'
  })
}
