import request from "@/utils/request";

// 查询报告模板列表
export function listTemplate(query) {
  return request({
    url: "/automate/template/list",
    method: "get",
    params: query,
  });
}

// 查询报告模板详细
export function getTemplate(id) {
  return request({
    url: "/automate/template/" + id,
    method: "get",
  });
}

// 新增报告模板
export function addTemplate(data) {
  return request({
    url: "/automate/template",
    method: "post",
    data: data,
  });
}

// 修改报告模板
export function updateTemplate(data) {
  return request({
    url: "/automate/template",
    method: "put",
    data: data,
  });
}

// 删除报告模板
export function delTemplate(id) {
  return request({
    url: "/automate/template/" + id,
    method: "delete",
  });
}

// 查询指标下拉框
export function getMetricList(query) {
  return request({
    url: "/automate/template/getMetricList",
    method: "get",
    params: query,
  });
}

// 查询设备类型下拉框
export function getResourceList(query) {
  return request({
    url: "/automate/template/getResourceList",
    method: "get",
    params: query,
  });
}

// 根据设备类型查询指标下拉框
export function getMetricListByResourceType(query) {
  return request({
    url: `/automate/template/getMetricListByResourceType/${query.id}`,
    method: "get",
    params: query,
  });
}


