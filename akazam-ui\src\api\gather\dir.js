import request from '@/utils/request'

// 查询MIB库文件夹列表
export function listDir(query) {
  return request({
    url: '/mib/dir/list',
    method: 'get',
    params: query
  })
}

// 查询MIB库文件夹详细
export function getDir(id) {
  return request({
    url: '/mib/dir/' + id,
    method: 'get'
  })
}

// 新增MIB库文件夹
export function addDir(data) {
  return request({
    url: '/mib/dir',
    method: 'post',
    data: data
  })
}

// 修改MIB库文件夹
export function updateDir(data) {
  return request({
    url: '/mib/dir',
    method: 'put',
    data: data
  })
}

// 删除MIB库文件夹
export function delDir(id) {
  return request({
    url: '/mib/dir/' + id,
    method: 'delete'
  })
}
