import request from '@/utils/request'

// 查询配置验收列表
export function listConfigAccept(query) {
  return request({
    url: '/automate/config/accept/list',
    method: 'get',
    params: query
  })
}

// 查询配置验收详细
export function getConfigAccept(id) {
  return request({
    url: '/automate/config/accept/' + id,
    method: 'get'
  })
}

// 新增配置验收
export function addConfigAccept(data) {
  return request({
    url: '/automate/config/accept',
    method: 'post',
    data: data
  })
}

// 修改配置验收
export function updateConfigAccept(data) {
  return request({
    url: '/automate/config/accept',
    method: 'put',
    data: data
  })
}

// 删除配置验收
export function delConfigAccept(id) {
  return request({
    url: '/automate/config/accept/' + id,
    method: 'delete'
  })
}
