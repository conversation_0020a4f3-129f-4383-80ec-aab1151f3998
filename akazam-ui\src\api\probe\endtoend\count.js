import request from '@/utils/request'

// 查询拨测统计日列表
export function listCount(query) {
  return request({
    url: '/probe/endtoend/statistics/count/list',
    method: 'get',
    params: query
  })
}

export function listCount2(query) {
  return request({
    url: '/probe/endtoend/statistics/count/task/list',
    method: 'get',
    params: query
  })
}


// 查询拨测统计日详细
export function getCount(id) {
  return request({
    url: '/probe/endtoend/statistics/count/' + id,
    method: 'get'
  })
}

// 新增拨测统计日
export function addCount(data) {
  return request({
    url: '/probe/endtoend/statistics/count',
    method: 'post',
    data: data
  })
}

// 修改拨测统计日
export function updateCount(data) {
  return request({
    url: '/probe/endtoend/statistics/count',
    method: 'put',
    data: data
  })
}

// 删除拨测统计日
export function delCount(id) {
  return request({
    url: '/probe/endtoend/statistics/count/' + id,
    method: 'delete'
  })
}

// 总监控次数
export function listCountType(query) {
  return request({
    url: '/probe/endtoend/statistics/count/type/list',
    method: 'get',
    params: query
  })
}
