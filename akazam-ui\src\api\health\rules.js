import request from '@/utils/request'

// 查询样本采集规则列表
export function listRules(query) {
  return request({
    url: '/health/s/c/r/list',
    method: 'get',
    params: query
  })
}

// 查询样本采集规则详细
export function getRules(id) {
  return request({
    url: '/health/s/c/r/' + id,
    method: 'get'
  })
}
export function getRulesDetail(id) {
  return request({
    url: '/health/s/c/r/detail/' + id,
    method: 'get'
  })
}

// 新增样本采集规则
export function addRules(data) {
  return request({
    url: '/health/s/c/r',
    method: 'post',
    data: data
  })
}

export function saveBase(data) {
  return request({
    url: '/health/s/c/r/save/base',
    method: 'post',
    data: data
  })
}

export function saveCollection(data) {
  return request({
    url: '/health/s/c/r/save/collection',
    method: 'post',
    data: data
  })
}

// 修改样本采集规则
export function updateRules(data) {
  return request({
    url: '/health/s/c/r',
    method: 'put',
    data: data
  })
}

// 删除样本采集规则
export function delRules(id) {
  return request({
    url: '/health/s/c/r/' + id,
    method: 'delete'
  })
}
