---
description: 
globs: 
alwaysApply: false
---
# 项目概述

此项目是一个基于 Vue 3 的自动化运维平台（Akazam），使用 Vite 作为构建工具。

## 技术栈

- **前端框架**: Vue 3.2
- **构建工具**: Vite 3.2
- **UI 框架**: Element Plus 2.6.3
- **状态管理**: Pinia 2.0
- **路由**: Vue Router 4.1
- **HTTP 客户端**: Axios 0.27
- **图表**: ECharts 5.4.3
- **编辑器**: Ace Editor, Vue Quill
- **终端模拟器**: xterm
- **可视化工具**: LogicFlow, datav-vue3

## 主要目录结构

- `src/api`: API 接口定义
- `src/components`: 公共组件
- `src/views`: 页面视图
- `src/router`: 路由配置
- `src/store`: Vuex 状态管理
- `src/stores`: Pinia 状态管理
- `src/assets`: 静态资源
- `src/utils`: 工具函数
- `src/layout`: 布局组件
- `src/directive`: 自定义指令
- `src/plugins`: 插件配置

## 入口文件

主入口文件是 [main.js](mdc:src/main.js)，应用路由配置在 [router/index.js](mdc:src/router/index.js)，主应用组件在 [App.vue](mdc:src/App.vue)。

