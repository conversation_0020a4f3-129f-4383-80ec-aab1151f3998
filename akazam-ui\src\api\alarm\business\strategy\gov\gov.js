import request from '@/utils/request'

// 查询告警中心-重要业务告警-政务微信告警策略（主）列表
export function listStrategy(query) {
  return request({
    url: '/business/strategy/gov/list',
    method: 'get',
    params: query
  })
}

// 查询告警中心-重要业务告警-政务微信告警策略（主）详细
export function getStrategy(id) {
  return request({
    url: '/business/strategy/gov/' + id,
    method: 'get'
  })
}

export function getMetric(id) {
  return request({
    url: '/business/strategy/gov/metric/' + id,
    method: 'get'
  })
}

// 新增告警中心-重要业务告警-政务微信告警策略（主）
export function addStrategy(data) {
  return request({
    url: '/business/strategy/gov',
    method: 'post',
    data: data
  })
}

// 修改告警中心-重要业务告警-政务微信告警策略（主）
export function updateStrategy(data) {
  return request({
    url: '/business/strategy/gov',
    method: 'put',
    data: data
  })
}

export function updateMetric(data) {
  return request({
    url: '/business/strategy/gov/metric',
    method: 'put',
    data: data
  })
}

// 删除告警中心-重要业务告警-政务微信告警策略（主）
export function delStrategy(id) {
  return request({
    url: '/business/strategy/gov/' + id,
    method: 'delete'
  })
}

export function delMetric(id) {
  return request({
    url: '/business/strategy/gov/metric/' + id,
    method: 'delete'
  })
}

export function getDeviceName() {
  return request({
    url: '/business/strategy/gov/name/',
    method: 'get'
  })
}
