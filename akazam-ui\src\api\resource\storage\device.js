import request from '@/utils/request'

// 查询存储设备列表
export function listStorageDevice(query) {
  return request({
    url: '/resource/storage/device/list',
    method: 'get',
    params: query
  })
}

// 查询存储设备详细
export function getStorageDevice(id) {
  return request({
    url: '/resource/storage/device/' + id,
    method: 'get'
  })
}

// 新增存储设备
export function addStorageDevice(data) {
  return request({
    url: '/resource/storage/device',
    method: 'post',
    data: data
  })
}

// 修改存储设备
export function updateStorageDevice(data) {
  return request({
    url: '/resource/storage/device',
    method: 'put',
    data: data
  })
}

// 删除存储设备
export function delStorageDevice(id) {
  return request({
    url: '/resource/storage/device/' + id,
    method: 'delete'
  })
}
