import request from '@/utils/request'

// 查询WAF故障探测-指标限制列表
export function listLimit(query) {
  return request({
    url: '/waf/limit/list',
    method: 'get',
    params: query
  })
}

// 查询WAF故障探测-指标限制详细
export function getLimit(id) {
  return request({
    url: '/waf/limit/' + id,
    method: 'get'
  })
}

// 新增WAF故障探测-指标限制
export function addLimit(data) {
  return request({
    url: '/waf/limit',
    method: 'post',
    data: data
  })
}

// 修改WAF故障探测-指标限制
export function updateLimit(data) {
  return request({
    url: '/waf/limit',
    method: 'put',
    data: data
  })
}

// 删除WAF故障探测-指标限制
export function delLimit(id) {
  return request({
    url: '/waf/limit/' + id,
    method: 'delete'
  })
}
