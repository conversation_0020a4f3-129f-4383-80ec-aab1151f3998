import request from "@/utils/request";

export function faultDetectWaf(query) {
  return request({
    url: "/fault/detect/waf",
    method: "get",
    params: query,
  });
}

export function faultDetectSite(data, devjson) {
  return request({
    url: "/fault/detect/site",
    method: "post",
    data: data,
    timeout: 60000,
    devjson: devjson ? devjson : null
  });
}

export function faultDetectSiteOne(query, devjson) {
  return request({
    url: `/fault/detect/siteDetect/${query.lbip}/${query.wafListenPort}`,
    method: "get",
    params: query,
    timeout: 60000,
    devjson: devjson ? devjson : null
  });
}



// 查询集群列表
export function faultWafCluster(query, devjson) {
  return request({
    url: "/fault/waf/cluster",
    method: "get",
    params: query,
    devjson: devjson ? devjson : null
  });
}

// 查询节点和代理站点列表
export function faultWafNodes(query, devjson) {
  return request({
    url: `/fault/waf/nodes/${query.clusterId}`,
    method: "get",
    params: query,
    devjson: devjson ? devjson : null
  });
}

// 查询节点指标列表
export function faultWafMetric(query, devjson) {
  return request({
    url: `/fault/waf/metric/${query.nodeId}`,
    method: "get",
    params: query,
    devjson: devjson ? devjson : null
  });
}

// waf故障探测
export function faultWafDetect(data, devjson) {
  return request({
    url: "/fault/waf/detect",
    method: "post",
    data: data,
    devjson: devjson ? devjson : null
  });
}

// WAF故障探测历史
export function faultWafDetectHistory(query, devjson) {
  return request({
    url: `/fault/waf/detect-history/${query.nodeId}`,
    method: "get",
    params: query,
    devjson: devjson ? devjson : null
  });
}

// 获取WAF探测记录详情
export function getWafRecordDetail(id) {
    return request({
        url: `/fault/waf-detect/record-detail/${id}`,
        method: 'get'
    })
}
