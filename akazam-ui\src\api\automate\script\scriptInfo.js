import request from '@/utils/request'

// 查询脚本信息列表
export function listScriptInfo(query) {
  return request({
    url: '/script/scriptInfo/list',
    method: 'get',
    params: query
  })
}

// 查询脚本信息详细
export function getScriptInfo(id) {
  return request({
    url: '/script/scriptInfo/' + id,
    method: 'get'
  })
}

// 新增脚本信息
export function addScriptInfo(data) {
  return request({
    url: '/script/scriptInfo',
    method: 'post',
    data: data
  })
}

// 修改脚本信息
export function updateScriptInfo(data) {
  return request({
    url: '/script/scriptInfo',
    method: 'put',
    data: data
  })
}

// 删除脚本信息
export function delScriptInfo(id) {
  return request({
    url: '/script/scriptInfo/' + id,
    method: 'delete'
  })
}


// 同步脚本信息
export function syncScript(id) {
  return request({
    url: `/script/scriptInfo/syn/${id}`,
    method: 'post',
  })
}