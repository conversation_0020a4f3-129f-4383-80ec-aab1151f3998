import request from '@/utils/request'

// 根据项目名称获取项目列表
export function getProjects(projectName) {
  return request({
    url: `/common/get-projects/${projectName}`,
    method: 'get'
  })
}
export function projectsQuery(projectName) {
  return request({
    url: `/common/projects/query?projectName=${projectName}`,
    method: 'get'
  })
}

export function getProjectsSelect(projectName) {
  return request({
    url: `/common/get-projects/select/${projectName}`,
    method: 'get'
  })
}

export function getProjectsSelectByCode(projectcode) {
  return request({
    url: `/common/get-projects/by-code/${projectcode}`,
    method: 'get'
  })
}
