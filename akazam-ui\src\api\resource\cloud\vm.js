import request from '@/utils/request'

// 查询云主机列表
export function listCloudVm(query) {
  return request({
    url: '/resource/cloud/vm/list',
    method: 'get',
    params: query
  })
}

// 查询云主机详细
export function getCloudVm(id) {
  return request({
    url: '/resource/cloud/vm/' + id,
    method: 'get'
  })
}

// 新增云主机
export function addCloudVm(data) {
  return request({
    url: '/resource/cloud/vm',
    method: 'post',
    data: data
  })
}

// 修改云主机
export function updateCloudVm(data) {
  return request({
    url: '/resource/cloud/vm',
    method: 'put',
    data: data
  })
}

// 删除云主机
export function delCloudVm(id) {
  return request({
    url: '/resource/cloud/vm/' + id,
    method: 'delete'
  })
}

// 查询虚拟机列表
export function getCloudVmList(query) {
  return request({
    url: '/resource/cloudVm/list',
    method: 'get',
    params: query
  })
}