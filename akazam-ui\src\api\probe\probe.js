import request from '@/utils/request'

// 查询探针管理列表
export function listProbe(query) {
  return request({
    url: '/probe/probe/list',
    method: 'get',
    params: query
  })
}

// 查询探针管理详细
export function getProbe(id) {
  return request({
    url: '/probe/probe/' + id,
    method: 'get'
  })
}

// 新增探针管理
export function addProbe(data) {
  return request({
    url: '/probe/probe',
    method: 'post',
    data: data
  })
}

// 修改探针管理
export function updateProbe(data) {
  return request({
    url: '/probe/probe',
    method: 'put',
    data: data
  })
}

// 删除探针管理
export function delProbe(id) {
  return request({
    url: '/probe/probe/' + id,
    method: 'delete'
  })
}
