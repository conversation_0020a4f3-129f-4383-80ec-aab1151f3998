---
description: 
globs: 
alwaysApply: false
---
# 可视化指南

Akazam 平台集成了多种可视化组件和库，用于数据展示和交互。

## 图表组件

主要使用 ECharts 进行数据可视化图表的绘制，封装在 `EcView` 组件中。

### ECharts 使用方式

基本使用方法：

```vue
<template>
  <echart :options="options" height="300px" />
</template>

<script setup>
import { reactive } from 'vue';
import echart from '@/components/EcView/index.vue';

const options = reactive({
  title: {
    text: '示例图表'
  },
  tooltip: {},
  xAxis: {
    data: ['衬衫', '羊毛衫', '雪纺衫', '裤子', '高跟鞋', '袜子']
  },
  yAxis: {},
  series: [{
    name: '销量',
    type: 'bar',
    data: [5, 20, 36, 10, 10, 20]
  }]
});
</script>
```

## 拓扑图

系统使用定制的拓扑图组件，位于 `src/components/Topo` 目录下。

拓扑图支持以下功能：

- 自动布局
- 拖拽节点
- 连线交互
- 缩放平移
- 节点分组
- 状态标记

## 数据流图

使用 LogicFlow 进行数据流图的绘制，支持自定义节点和边。

```vue
<template>
  <div class="flow-container">
    <logic-flow :config="config" />
  </div>
</template>

<script setup>
import LogicFlow from '@/components/LFComponents/LogicFlow.vue';

const config = {
  width: 1000,
  height: 600,
  grid: true,
  background: {
    color: '#f5f5f5'
  }
};
</script>
```

## 可视化动效

系统提供多种可视化动效组件：

- `InteractiveLines`: 交互式连线动效
- `VueMatrixDigitRain`: 矩阵数字雨效果
- `src/datavvue3`: 基于 DataV 的大屏可视化组件

## 大屏展示

平台支持大屏展示模式，主要用于监控和可视化场景。大屏相关组件在 `src/views/visual` 目录下。

大屏设计原则：

1. 使用栅格布局，确保响应式适配
2. 使用深色背景，提高对比度
3. 减少文字信息，增加图形化展示
4. 关键信息突出显示
5. 数据自动刷新

## 地图可视化

系统整合了多种地图可视化方案：

- ECharts 地图
- OpenLayers 地图
- 百度/高德地图 API

地图组件位于 `src/components/Map` 目录下。

