import request from '@/utils/request'

// 查询健康告警管理列表
export function listAlarm(query, devjson) {
  return request({
    url: '/health/alarm/list',
    method: 'get',
    params: query,
    devjson: devjson ? devjson : false
  })
}

// 查询健康告警管理详细
export function getAlarm(id) {
  return request({
    url: '/health/alarm/' + id,
    method: 'get'
  })
}

// 新增健康告警管理
export function addAlarm(data) {
  return request({
    url: '/health/alarm',
    method: 'post',
    data: data
  })
}

// 修改健康告警管理
export function updateAlarm(data) {
  return request({
    url: '/health/alarm',
    method: 'put',
    data: data
  })
}

// 删除健康告警管理
export function delAlarm(id) {
  return request({
    url: '/health/alarm/' + id,
    method: 'delete'
  })
}
