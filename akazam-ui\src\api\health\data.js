import request from '@/utils/request'

// 查询样本列表
export function listData(query) {
  return request({
    url: '/health/data/list',
    method: 'get',
    params: query
  })
}

// 查询样本详细
export function getData(id) {
  return request({
    url: '/health/data/' + id,
    method: 'get'
  })
}

// 新增样本
export function addData(data) {
  return request({
    url: '/health/data',
    method: 'post',
    data: data
  })
}

// 修改样本
export function updateData(data) {
  return request({
    url: '/health/data',
    method: 'put',
    data: data
  })
}

// 删除样本
export function delData(id) {
  return request({
    url: '/health/data/' + id,
    method: 'delete'
  })
}
