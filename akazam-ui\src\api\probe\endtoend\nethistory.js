import request from '@/utils/request'

// 查询网络信息历史记录列表
export function listNethistory(query) {
  return request({
    url: '/probe/endtoend/nethistory/list',
    method: 'get',
    params: query
  })
}

// 查询网络信息历史记录详细
export function getNethistory(id) {
  return request({
    url: '/probe/endtoend/nethistory/' + id,
    method: 'get'
  })
}

// 新增网络信息历史记录
export function addNethistory(data) {
  return request({
    url: '/probe/endtoend/nethistory',
    method: 'post',
    data: data
  })
}

// 修改网络信息历史记录
export function updateNethistory(data) {
  return request({
    url: '/probe/endtoend/nethistory',
    method: 'put',
    data: data
  })
}

// 删除网络信息历史记录
export function delNethistory(id) {
  return request({
    url: '/probe/endtoend/nethistory/' + id,
    method: 'delete'
  })
}


// 图表
export function chartNethistory(query) {
  return request({
    url: '/probe/endtoend/nethistory/chart',
    method: 'get',
    params: query
  })
}

// 地图
export function mapNethistory(query) {
  return request({
    url: '/probe/endtoend/nethistory/map/overview',
    method: 'get',
    params: query
  })
}

