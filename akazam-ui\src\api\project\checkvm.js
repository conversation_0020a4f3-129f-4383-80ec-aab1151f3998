import request from '@/utils/request'

// 查询项目附加信息列表
export function listCheckvm(query) {
  return request({
    url: '/project/checkvm/list',
    method: 'get',
    params: query
  })
}

// 查询项目附加信息详细
export function getCheckvm(id) {
  return request({
    url: '/project/checkvm/' + id,
    method: 'get'
  })
}

// 新增项目附加信息
export function addCheckvm(data) {
  return request({
    url: '/project/checkvm',
    method: 'post',
    data: data
  })
}

// 修改项目附加信息
export function updateCheckvm(data) {
  return request({
    url: '/project/checkvm',
    method: 'put',
    data: data
  })
}

// 删除项目附加信息
export function delCheckvm(id) {
  return request({
    url: '/project/checkvm/' + id,
    method: 'delete'
  })
}
