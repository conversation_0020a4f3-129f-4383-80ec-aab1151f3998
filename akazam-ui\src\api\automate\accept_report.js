import request from '@/utils/request'

// 查询验收报告列表
export function listReport(query) {
  return request({
    url: '/automate/accept/report/list',
    method: 'get',
    params: query
  })
}

// 查询验收报告详细
export function getReport(id) {
  return request({
    url: '/automate/accept/report/' + id,
    method: 'get'
  })
}

// 新增验收报告
export function addReport(data) {
  return request({
    url: '/automate/accept/report',
    method: 'post',
    data: data
  })
}

// 修改验收报告
export function updateReport(data) {
  return request({
    url: '/automate/accept/report',
    method: 'put',
    data: data
  })
}

// 删除验收报告
export function delReport(id) {
  return request({
    url: '/automate/accept/report/' + id,
    method: 'delete'
  })
}
