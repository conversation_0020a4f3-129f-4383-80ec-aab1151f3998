import request from '@/utils/request'

// 查询业务访问清单列表
export function listBusinessaccess(query) {
  return request({
    url: '/monitor/businessaccess/list',
    method: 'get',
    params: query
  })
}

// 查询业务访问清单详细
export function getBusinessaccess(id) {
  return request({
    url: '/monitor/businessaccess/' + id,
    method: 'get'
  })
}

// 新增业务访问清单
export function addBusinessaccess(data) {
  return request({
    url: '/monitor/businessaccess',
    method: 'post',
    data: data
  })
}

// 修改业务访问清单
export function updateBusinessaccess(data) {
  return request({
    url: '/monitor/businessaccess',
    method: 'put',
    data: data
  })
}

// 删除业务访问清单
export function delBusinessaccess(id) {
  return request({
    url: '/monitor/businessaccess/' + id,
    method: 'delete'
  })
}
