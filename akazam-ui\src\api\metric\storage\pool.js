import request from '@/utils/request'

// 查询存储池指标列表
export function listStoragePool(query) {
  return request({
    url: '/metric/storage/pool/list',
    method: 'get',
    params: query
  })
}

// 查询存储池指标详细
export function getStoragePool(id) {
  return request({
    url: '/metric/storage/pool/' + id,
    method: 'get'
  })
}

// 新增存储池指标
export function addStoragePool(data) {
  return request({
    url: '/metric/storage/pool',
    method: 'post',
    data: data
  })
}

// 修改存储池指标
export function updateStoragePool(data) {
  return request({
    url: '/metric/storage/pool',
    method: 'put',
    data: data
  })
}

// 删除存储池指标
export function delStoragePool(id) {
  return request({
    url: '/metric/storage/pool/' + id,
    method: 'delete'
  })
}
