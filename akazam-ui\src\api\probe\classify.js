import request from '@/utils/request'

// 查询探针分类列表
export function listClassify(query) {
  return request({
    url: '/probe/classify/list',
    method: 'get',
    params: query
  })
}

// 查询探针分类详细
export function getClassify(id) {
  return request({
    url: '/probe/classify/' + id,
    method: 'get'
  })
}

// 新增探针分类
export function addClassify(data) {
  return request({
    url: '/probe/classify',
    method: 'post',
    data: data
  })
}

// 修改探针分类
export function updateClassify(data) {
  return request({
    url: '/probe/classify',
    method: 'put',
    data: data
  })
}

// 删除探针分类
export function delClassify(id) {
  return request({
    url: '/probe/classify/' + id,
    method: 'delete'
  })
}
