import request from '@/utils/request'

// 查询模板信息列表
export function listTemplateInfo(query) {
  return request({
    url: '/template/templateInfo/list',
    method: 'get',
    params: query
  })
}

// 查询模板信息详细
export function getTemplateInfo(id) {
  return request({
    url: '/template/templateInfo/' + id,
    method: 'get'
  })
}

// 新增模板信息
export function addTemplateInfo(data) {
  return request({
    url: '/template/templateInfo',
    method: 'post',
    data: data
  })
}

// 修改模板信息
export function updateTemplateInfo(data) {
  return request({
    url: '/template/templateInfo',
    method: 'put',
    data: data
  })
}

export function copyTemplate(data) {
  return request({
    url: '/template/templateInfo/copyTemplate',
    method: 'post',
    data: data
  })
}

// 删除模板信息
export function delTemplateInfo(id) {
  return request({
    url: '/template/templateInfo/' + id,
    method: 'delete'
  })
}


// 查询模板信息列表
export function listViewList(query) {
  return request({
    url: '/template/templateInfo/viewList',
    method: 'get',
    params: query
  })
}

// 查询模板信息列表
export function listExecList(query) {
  return request({
    url: '/template/templateInfo/execList',
    method: 'get',
    params: query
  })
}

export function listAllTemplate() {
  return request({
    url: '/template/templateInfo/list/all',
    method: 'get'
  })
}