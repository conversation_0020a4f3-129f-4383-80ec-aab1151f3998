import request from '@/utils/request'

// 查询事件单列表
export function listOrder(query) {
  return request({
    url: '/fault/order/list',
    method: 'get',
    params: query
  })
}

// 查询事件单详细
export function getOrder(id) {
  return request({
    url: '/fault/order/' + id,
    method: 'get'
  })
}

// 新增事件单
export function addOrder(data) {
  return request({
    url: '/fault/order',
    method: 'post',
    data: data
  })
}

// 修改事件单
export function updateOrder(data) {
  return request({
    url: '/fault/order',
    method: 'put',
    data: data
  })
}

// 删除事件单
export function delOrder(id) {
  return request({
    url: '/fault/order/' + id,
    method: 'delete'
  })
}


export function projectInfoTree(query) {
  return request({
    url: '/project/info/tree',
    method: 'get',
    params: query
  })
}

export function projectInfoTreeType(query,devjson) {
  return request({
    url: '/project/info/tree/type',
    method: 'get',
    params: query,
    devjson: devjson ? devjson : null
  })
}