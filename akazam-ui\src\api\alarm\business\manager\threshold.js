import request from "@/utils/request";

// 查询告警中心-重要业务告警-告警管理列表
export function listAlarm(query, devjson) {
  return request({
    url: "/alarm/business/threshold/list",
    method: "get",
    params: query,
    devjson: devjson ? devjson : null,
  });
}

// 查询告警中心-重要业务告警-告警管理详细
export function getAlarm(id) {
  return request({
    url: "/alarm/business/threshold/" + id,
    method: "get",
  });
}

// 新增告警中心-重要业务告警-告警管理
export function addAlarm(data) {
  return request({
    url: "/alarm/business/threshold",
    method: "post",
    data: data,
  });
}

// 修改告警中心-重要业务告警-告警管理
export function updateAlarm(data) {
  return request({
    url: "/alarm/business/threshold",
    method: "put",
    data: data,
  });
}

// 删除告警中心-重要业务告警-告警管理
export function delAlarm(id) {
  return request({
    url: "/alarm/business/threshold/" + id,
    method: "delete",
  });
}

// 查询告警中心-探测站点告警-告警管理列表
export function listAlarmEnd2end(query, devjson) {
  return request({
    url: "/monitor/end2end/probe/alarm/list",
    method: "get",
    params: query,
    devjson: devjson ? devjson : null,
  });
}
