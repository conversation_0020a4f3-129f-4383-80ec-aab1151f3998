import request from '@/utils/request'

// 查询作业执行历史列表
export function listJob_record(query) {
  return request({
    url: '/automate/job_record/list',
    method: 'get',
    params: query
  })
}

// 查询作业执行报告
export function getJobReport(query, devjson) {
  return request({
    url: '/automate/job_record/getJobReport',
    method: 'get',
    timeout: 300000, // 设置请求超时时间为10分钟
    params: query,
    devjson: devjson ? devjson : false
  })
}

// 查询作业执行历史详细
export function getJob_record(id) {
  return request({
    url: '/automate/job_record/' + id,
    method: 'get'
  })
}

// 新增作业执行历史
export function addJob_record(data) {
  return request({
    url: '/automate/job_record',
    method: 'post',
    data: data
  })
}

// 修改作业执行历史
export function updateJob_record(data) {
  return request({
    url: '/automate/job_record',
    method: 'put',
    data: data
  })
}

// 删除作业执行历史
export function delJob_record(id) {
  return request({
    url: '/automate/job_record/' + id,
    method: 'delete'
  })
}

export function listJob_record_statistic(query) {
  return request({
    url: '/automate/job_record/statistic',
    method: 'get',
    params: query
  })
}