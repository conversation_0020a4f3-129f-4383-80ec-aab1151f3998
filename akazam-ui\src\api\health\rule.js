import request from '@/utils/request'

// 查询健康告警规则列表
export function listRule(query) {
  return request({
    url: '/health/rule/list',
    method: 'get',
    params: query
  })
}

// 查询健康告警规则详细
export function getRule(id) {
  return request({
    url: '/health/rule/' + id,
    method: 'get'
  })
}

// 新增健康告警规则
export function addRule(data) {
  return request({
    url: '/health/rule',
    method: 'post',
    data: data
  })
}

// 修改健康告警规则
export function updateRule(data) {
  return request({
    url: '/health/rule',
    method: 'put',
    data: data
  })
}

// 删除健康告警规则
export function delRule(id) {
  return request({
    url: '/health/rule/' + id,
    method: 'delete'
  })
}

export function switchOn() {
  return request({
    url: '/health/rule/base-line-switch/ON',
    method: 'get'
  })
}

export function switchOff() {
  return request({
    url: '/health/rule/base-line-switch/OFF',
    method: 'get'
  })
}