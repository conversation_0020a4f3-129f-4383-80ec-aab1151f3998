import request from '@/utils/request'

// 查询物理机性能指标列表
export function listPhysical(query) {
  return request({
    url: '/metric/physical/list',
    method: 'get',
    params: query
  })
}

// 查询物理机性能指标详细
export function getPhysical(id) {
  return request({
    url: '/metric/physical/' + id,
    method: 'get'
  })
}

// 新增物理机性能指标
export function addPhysical(data) {
  return request({
    url: '/metric/physical',
    method: 'post',
    data: data
  })
}

// 修改物理机性能指标
export function updatePhysical(data) {
  return request({
    url: '/metric/physical',
    method: 'put',
    data: data
  })
}

// 删除物理机性能指标
export function delPhysical(id) {
  return request({
    url: '/metric/physical/' + id,
    method: 'delete'
  })
}
