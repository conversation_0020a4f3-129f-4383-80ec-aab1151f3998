import request from '@/utils/request'

// 查询端到端探针列表
export function listAgent(query) {
  return request({
    url: '/probe/endtoend/agent/list',
    method: 'get',
    params: query
  })
}

// 查询端到端探针详细
export function getAgent(id) {
  return request({
    url: '/probe/endtoend/agent/' + id,
    method: 'get'
  })
}

// 新增端到端探针
export function addAgent(data) {
  return request({
    url: '/probe/endtoend/agent',
    method: 'post',
    data: data
  })
}

// 修改端到端探针
export function updateAgent(data) {
  return request({
    url: '/probe/endtoend/agent',
    method: 'put',
    data: data
  })
}

// 删除端到端探针
export function delAgent(id) {
  return request({
    url: '/probe/endtoend/agent/' + id,
    method: 'delete',
    timeout: 600000
  })
}


export function allAgent(query) {
  return request({
    url: '/probe/endtoend/agent/all',
    method: 'get',
    params: query
  })
}
export function getAgentList(query) {
  return request({
    url: '/probe/endtoend/agent/getAgentList/1',
    method: 'get',
    params: query
  })
}
export function getEndToEndList(query) {
  return request({
    url: '/probe/endtoend/agent/options',
    method: 'get',
    params: query
  })
}
