import request from '@/utils/request'

// 查询日志异常告警-告警规则-基于分类告警列表
export function listLevel(query) {
  return request({
    url: '/alarm/level/list',
    method: 'get',
    params: query
  })
}

// 查询日志异常告警-告警规则-基于分类告警详细
export function getLevel(id) {
  return request({
    url: '/alarm/level/' + id,
    method: 'get'
  })
}

// 新增日志异常告警-告警规则-基于分类告警
export function addLevel(data) {
  return request({
    url: '/alarm/level',
    method: 'post',
    data: data
  })
}

// 修改日志异常告警-告警规则-基于分类告警
export function updateLevel(data) {
  return request({
    url: '/alarm/level',
    method: 'put',
    data: data
  })
}

// 删除日志异常告警-告警规则-基于分类告警
export function delLevel(id) {
  return request({
    url: '/alarm/level/' + id,
    method: 'delete'
  })
}
