import request from '@/utils/request'

// 查询tcp拨测列表
export function listTcp(query) {
  return request({
    url: '/probe/endtoend/tcp/list',
    method: 'get',
    params: query
  })
}

// 查询tcp拨测详细
export function getTcp(id) {
  return request({
    url: '/probe/endtoend/tcp/' + id,
    method: 'get'
  })
}

// 新增tcp拨测
export function addTcp(data) {
  return request({
    url: '/probe/endtoend/tcp',
    method: 'post',
    data: data
  })
}

// 修改tcp拨测
export function updateTcp(data) {
  return request({
    url: '/probe/endtoend/tcp',
    method: 'put',
    data: data
  })
}

// 删除tcp拨测
export function delTcp(id) {
  return request({
    url: '/probe/endtoend/tcp/' + id,
    method: 'delete'
  })
}

// 启用暂停
export function suspendTcp(id, suspend) {
  return request({
    url: '/probe/endtoend/tcp/suspend/' + id + '?suspend=' + suspend,
    method: 'post',
  })
}