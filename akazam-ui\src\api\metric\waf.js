import request from '@/utils/request'

// 查询WAF指标列表
export function listWaf(query) {
  return request({
    url: '/metric/waf/list',
    method: 'get',
    params: query
  })
}

// 查询WAF指标详细
export function getWaf(id) {
  return request({
    url: '/metric/waf/' + id,
    method: 'get'
  })
}

// 新增WAF指标
export function addWaf(data) {
  return request({
    url: '/metric/waf',
    method: 'post',
    data: data
  })
}

// 修改WAF指标
export function updateWaf(data) {
  return request({
    url: '/metric/waf',
    method: 'put',
    data: data
  })
}

// 删除WAF指标
export function delWaf(id) {
  return request({
    url: '/metric/waf/' + id,
    method: 'delete'
  })
}
