import request from '@/utils/request'

// 查询项目云主机列表
export function listCloudvm(query) {
  return request({
    url: '/project/cloudvm/list',
    method: 'get',
    params: query
  })
}

// 查询项目云主机详细
export function getCloudvm(id) {
  return request({
    url: '/project/cloudvm/' + id,
    method: 'get'
  })
}

// 新增项目云主机
export function addCloudvm(data) {
  return request({
    url: '/project/cloudvm',
    method: 'post',
    data: data
  })
}

// 修改项目云主机
export function updateCloudvm(data) {
  return request({
    url: '/project/cloudvm',
    method: 'put',
    data: data
  })
}

// 删除项目云主机
export function delCloudvm(id) {
  return request({
    url: '/project/cloudvm/' + id,
    method: 'delete'
  })
}
