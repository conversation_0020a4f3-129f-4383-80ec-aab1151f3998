import request from '@/utils/request'

// 查询云管告警处置结果列表
export function listHandleResult(query) {
  return request({
    url: '/alarm/handleResult/list',
    method: 'get',
    params: query
  })
}

// 查询云管告警处置结果详细
export function getHandleResult(id) {
  return request({
    url: '/alarm/handleResult/' + id,
    method: 'get'
  })
}

// 新增云管告警处置结果
export function addHandleResult(data) {
  return request({
    url: '/alarm/handleResult',
    method: 'post',
    data: data
  })
}

// 修改云管告警处置结果
export function updateHandleResult(data) {
  return request({
    url: '/alarm/handleResult',
    method: 'put',
    data: data
  })
}

// 删除云管告警处置结果
export function delHandleResult(id) {
  return request({
    url: '/alarm/handleResult/' + id,
    method: 'delete'
  })
}
