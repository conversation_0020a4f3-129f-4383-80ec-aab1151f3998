import request from '@/utils/request'

// 查询作业执行触发列表
export function listAlarmtrigger(query) {
  return request({
    url: '/job/alarmtrigger/list',
    method: 'get',
    params: query
  })
}

// 查询作业执行触发详细
export function getAlarmtrigger(id) {
  return request({
    url: '/job/alarmtrigger/' + id,
    method: 'get'
  })
}

// 新增作业执行触发
export function addAlarmtrigger(data) {
  return request({
    url: '/job/alarmtrigger',
    method: 'post',
    data: data
  })
}

// 修改作业执行触发
export function updateAlarmtrigger(data) {
  return request({
    url: '/job/alarmtrigger',
    method: 'put',
    data: data
  })
}

// 删除作业执行触发
export function delAlarmtrigger(id) {
  return request({
    url: '/job/alarmtrigger/' + id,
    method: 'delete'
  })
}
