import request from '@/utils/request'

// 查询关注列-模型关系列表
export function listModel(query) {
  return request({
    url: '/health/model/list',
    method: 'get',
    params: query
  })
}

// 查询关注列-模型关系详细
export function getModel(id) {
  return request({
    url: '/health/model/' + id,
    method: 'get'
  })
}

// 新增关注列-模型关系
export function addModel(data) {
  return request({
    url: '/health/model',
    method: 'post',
    data: data
  })
}

// 修改关注列-模型关系
export function updateModel(data) {
  return request({
    url: '/health/model',
    method: 'put',
    data: data
  })
}

// 删除关注列-模型关系
export function delModel(id) {
  return request({
    url: '/health/model/' + id,
    method: 'delete'
  })
}

export function examiningReport(query) {
  return request({
    url: '/health/detection/check',
    method: 'get',
    params: query,
    timeout: 60000
  })
}