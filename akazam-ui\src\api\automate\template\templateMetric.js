import request from '@/utils/request'

// 查询模板指标信息列表
export function listTemplateMetric(query) {
  return request({
    url: '/template/templateMetric/list',
    method: 'get',
    params: query
  })
}

// 查询模板指标信息详细
export function getTemplateMetric(id) {
  return request({
    url: '/template/templateMetric/' + id,
    method: 'get'
  })
}

// 新增模板指标信息
export function addTemplateMetric(data) {
  return request({
    url: '/template/templateMetric',
    method: 'post',
    data: data
  })
}

// 修改模板指标信息
export function updateTemplateMetric(data) {
  return request({
    url: '/template/templateMetric',
    method: 'put',
    data: data
  })
}

// 删除模板指标信息
export function delTemplateMetric(id) {
  return request({
    url: '/template/templateMetric/' + id,
    method: 'delete'
  })
}
