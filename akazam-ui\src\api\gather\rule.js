import request from '@/utils/request'

// 查询SNMP采集规则主列表
export function listRule(query) {
  return request({
    url: '/gather/rule/list',
    method: 'get',
    params: query
  })
}

// 查询SNMP采集规则主详细
export function getRule(id) {
  return request({
    url: '/gather/rule/' + id,
    method: 'get'
  })
}

// 新增SNMP采集规则主
export function addRule(data) {
  return request({
    url: '/gather/rule',
    method: 'post',
    data: data
  })
}

// 修改SNMP采集规则主
export function updateRule(data) {
  return request({
    url: '/gather/rule',
    method: 'put',
    data: data
  })
}

// 删除SNMP采集规则主
export function delRule(id) {
  return request({
    url: '/gather/rule/' + id,
    method: 'delete'
  })
}

export function mibOptions() {
  return request({
    url: '/gather/rule/mibOptions',
    method: 'get'
  })
}

export function attrOptions(row) {
  return request({
    url: '/gather/rule/attrOptions?mibId=' + row.mibId +  '&pageNum=' + row.pageNum +  '&pageSize=' + row.pageSize + '&keyword=' + row.keyword,
    method: 'get'
  })
}

// 通过关键字筛选MIB库
export function searchMibByKeyword(params) {
  return request({
    url: '/gather/rule/searchMib',
    method: 'get',
    params: params
  })
}
