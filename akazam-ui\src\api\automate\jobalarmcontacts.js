import request from '@/utils/request'

// 查询作业执行告警联系人列表
export function listAlarmcontacts(query) {
  return request({
    url: '/job/alarmcontacts/list',
    method: 'get',
    params: query
  })
}

// 查询作业执行告警联系人详细
export function getAlarmcontacts(id) {
  return request({
    url: '/job/alarmcontacts/' + id,
    method: 'get'
  })
}

// 新增作业执行告警联系人
export function addAlarmcontacts(data) {
  return request({
    url: '/job/alarmcontacts',
    method: 'post',
    data: data
  })
}

// 修改作业执行告警联系人
export function updateAlarmcontacts(data) {
  return request({
    url: '/job/alarmcontacts',
    method: 'put',
    data: data
  })
}

// 删除作业执行告警联系人
export function delAlarmcontacts(id) {
  return request({
    url: '/job/alarmcontacts/' + id,
    method: 'delete'
  })
}
