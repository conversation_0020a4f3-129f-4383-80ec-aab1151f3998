import request from '@/utils/request'

// 查询探针子系统列表
export function listAgent(query) {
  return request({
    url: '/agent/agent/list',
    method: 'get',
    params: query
  })
}

// 查询探针子系统详细
export function getAgent(id) {
  return request({
    url: '/agent/agent/' + id,
    method: 'get'
  })
}

// 新增探针子系统
export function addAgent(data) {
  return request({
    url: '/agent/agent',
    method: 'post',
    data: data
  })
}

// 修改探针子系统
export function updateAgent(data) {
  return request({
    url: '/agent/agent',
    method: 'put',
    data: data
  })
}

// 删除探针子系统
export function delAgent(id) {
  return request({
    url: '/agent/agent/' + id,
    method: 'delete'
  })
}
