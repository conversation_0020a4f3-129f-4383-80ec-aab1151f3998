import request from '@/utils/request'

// 查询tcp信息历史记录列表
export function listTcphistory(query) {
  return request({
    url: '/probe/endtoend/tcphistory/list',
    method: 'get',
    params: query
  })
}

// 查询tcp信息历史记录详细
export function getTcphistory(id) {
  return request({
    url: '/probe/endtoend/tcphistory/' + id,
    method: 'get'
  })
}

// 新增tcp信息历史记录
export function addTcphistory(data) {
  return request({
    url: '/probe/endtoend/tcphistory',
    method: 'post',
    data: data
  })
}

// 修改tcp信息历史记录
export function updateTcphistory(data) {
  return request({
    url: '/probe/endtoend/tcphistory',
    method: 'put',
    data: data
  })
}

// 删除tcp信息历史记录
export function delTcphistory(id) {
  return request({
    url: '/probe/endtoend/tcphistory/' + id,
    method: 'delete'
  })
}



// 图表
export function chartTcphistory(query) {
  return request({
    url: '/probe/endtoend/tcphistory/chart',
    method: 'get',
    params: query
  })
}

// 地图
export function mapTcphistory(query) {
  return request({
    url: '/probe/endtoend/tcphistory/map/overview',
    method: 'get',
    params: query
  })
}
