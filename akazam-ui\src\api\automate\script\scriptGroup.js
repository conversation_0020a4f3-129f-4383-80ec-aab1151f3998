import request from '@/utils/request'

// 查询脚本分组列表
export function listScriptGroup(query) {
  return request({
    url: '/script/scriptGroup/list',
    method: 'get',
    params: query || { pageSize: 9999 }
  })
}

// 查询脚本分组详细
export function getScriptGroup(id) {
  return request({
    url: '/script/scriptGroup/' + id,
    method: 'get'
  })
}

// 新增脚本分组
export function addScriptGroup(data) {
  return request({
    url: '/script/scriptGroup',
    method: 'post',
    data: data
  })
}

// 修改脚本分组
export function updateScriptGroup(data) {
  return request({
    url: '/script/scriptGroup',
    method: 'put',
    data: data
  })
}

// 删除脚本分组
export function delScriptGroup(id) {
  return request({
    url: '/script/scriptGroup/' + id,
    method: 'delete'
  })
}
